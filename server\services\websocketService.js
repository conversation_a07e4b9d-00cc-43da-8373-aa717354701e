// WebSocket 服務實現
const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const { URL } = require('url');
const { ObjectId } = require('mongodb');
const EventEmitter = require('events');

// 引入 rawdata 壓縮模組
const rawdataCompression = require('../utils/rawdataCompression');
const { RAWDATA_FORMATS } = rawdataCompression;

// 定義簡短的顏色類型代碼轉換函數
const shortCodeToDisplayColorType = (code) => {
  if (!code) return 'UNKNOWN';

  switch (code.toUpperCase()) {
    case 'BW':
      return 'Gray16';
    case 'BWR':
      return 'Black & White & Red';
    case 'BWRY':
      return 'Black & White & Red & Yellow';
    case 'ALL':
      return 'All colors';
    default:
      console.warn(`未知的顏色類型代碼: ${code}，將顯示為 UNKNOWN`);
      return 'UNKNOWN';
  }
};

// 共享的數據庫連接
let getDbConnection = null;

// WebSocket 伺服器
let wss = null;

// 用於存儲連接的網關
const connectedGateways = new Map();

// 存儲Gateway能力信息
const gatewayCapabilities = new Map(); // macAddress -> chunkingSupport

// 新增：Gateway 偏好格式存儲
const gatewayPreferredFormats = new Map(); // macAddress -> preferredFormat

// 存儲正在進行的chunk傳輸狀態
const activeChunkTransmissions = new Map(); // gatewayId -> Map(chunkId -> { chunkId, deviceMac, startTime, status })

// 網關狀態變化事件發射器
const gatewayStatusEmitter = new EventEmitter();

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

// 獲取網關集合
const getGatewayCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('gateways');
  return { collection, client };
};

// 獲取設備集合
const getDeviceCollection = async () => {
  if (!getDbConnection) {
    throw new Error('資料庫連接函數尚未初始化');
  }
  const { client, db } = await getDbConnection();
  const collection = db.collection('devices');
  return { collection, client };
};

// 重置所有網關狀態為離線
const resetAllGatewayStatus = async () => {
  try {
    const { collection } = await getGatewayCollection();

    // 將所有網關狀態設為 offline
    const result = await collection.updateMany(
      { status: 'online' }, // 只更新目前為 online 的網關
      {
        $set: {
          status: 'offline',
          updatedAt: new Date()
        }
      }
    );

    if (result.modifiedCount > 0) {
      console.log(`已重置 ${result.modifiedCount} 個網關狀態為離線`);
    } else {
      console.log('沒有需要重置狀態的網關');
    }
  } catch (error) {
    console.error('重置網關狀態失敗:', error);
  }
};

// 初始化 WebSocket 服務
const initWebSocketServer = async (server, jwtSecret) => {
  // 首先重置所有網關狀態為離線
  await resetAllGatewayStatus();

  wss = new WebSocket.Server({
    server,
    // 不使用固定路徑，而是在 verifyClient 中解析路徑
    verifyClient: async (info, callback) => {
      try {
        // 獲取請求URL
        const url = new URL(info.req.url, `http://${info.req.headers.host}`);
        const token = url.searchParams.get('token');

        // 檢查是否為前端客戶端連接（沒有token或路徑不包含gateway）
        const pathParts = url.pathname.split('/');
        const isGatewayConnection = pathParts.includes('gateway');

        if (!isGatewayConnection) {
          // 前端客戶端連接
          if (!token) {
            // 對於開發環境，允許無token的連接
            if (process.env.NODE_ENV === 'development') {
              console.warn('開發環境：允許無token的前端連接');
              info.req.clientType = 'frontend';
              info.req.userId = 'dev-user';
              info.req.username = 'Development User';
              return callback(true);
            }
            console.error('前端WebSocket連接缺少token');
            return callback(false, 401, 'Unauthorized');
          }

          try {
            const decoded = jwt.verify(token, jwtSecret);
            // 檢查是否為用戶類型的token（用戶token的type可能是undefined、'user'或'websocket'）
            if (decoded.type && decoded.type !== 'user' && decoded.type !== 'websocket' && decoded.type !== 'gateway') {
              console.error('前端WebSocket token類型不匹配:', decoded.type);
              return callback(false, 401, 'Invalid token type');
            }

            // 如果是用戶token，驗證通過，標記為前端客戶端
            info.req.clientType = 'frontend';
            info.req.userId = decoded.userId || decoded.id;
            info.req.username = decoded.username || decoded.name;
            info.req.token = decoded;
            console.log(`前端客戶端連接驗證成功: ${decoded.username || decoded.name || 'Unknown'} (token類型: ${decoded.type || 'user'})`);
            return callback(true);
          } catch (error) {
            console.error('前端WebSocket認證錯誤:', error);
            // 對於開發環境，允許無效token的連接（僅用於測試）
            if (process.env.NODE_ENV === 'development') {
              console.warn('開發環境：允許無效token的前端連接');
              info.req.clientType = 'frontend';
              info.req.userId = 'dev-user';
              info.req.username = 'Development User';
              return callback(true);
            }
            return callback(false, 401, 'Authentication failed');
          }
        }

        // 網關連接驗證邏輯
        if (!token) {
          console.error('WebSocket 連接缺少 token');
          return callback(false, 401, 'Unauthorized');
        }

        // 驗證 token
        const decoded = jwt.verify(token, jwtSecret);

        // 檢查是否為網關類型的 token
        if (decoded.type !== 'gateway') {
          console.error('WebSocket token 類型不匹配');
          return callback(false, 401, 'Invalid token type');
        }

        // 解析路徑 (例如 /ws/store/{storeId}/gateway/{gatewayId})
        const storeIdFromPath = pathParts[pathParts.indexOf('store') + 1];
        const gatewayIdFromPath = pathParts[pathParts.indexOf('gateway') + 1];

        // 驗證 token 中的網關 ID 與路徑中的網關 ID 是否匹配
        if (gatewayIdFromPath && gatewayIdFromPath !== decoded.gatewayId) {
          console.error('WebSocket 路徑中的網關 ID 與 token 不匹配');
          return callback(false, 403, 'Gateway ID mismatch');
        }

        // 驗證 token 中的商店 ID 與路徑中的商店 ID 是否匹配
        if (storeIdFromPath && storeIdFromPath !== decoded.storeId) {
          console.error('WebSocket 路徑中的商店 ID 與 token 不匹配');
          return callback(false, 403, 'Store ID mismatch');
        }

        // 檢查 token 中是否包含 MAC 地址
        if (!decoded.macAddress) {
          console.error('WebSocket token 缺少 MAC 地址');
          return callback(false, 403, 'MAC address missing in token');
        }

        // 驗證通過，將 token 數據附加到 req 對象
        info.req.clientType = 'gateway';
        info.req.gatewayId = decoded.gatewayId;
        info.req.storeId = decoded.storeId;
        info.req.macAddress = decoded.macAddress; // 添加 MAC 地址到 req 對象
        info.req.token = decoded;

        callback(true);
      } catch (error) {
        console.error('WebSocket 認證錯誤:', error);
        callback(false, 401, 'Authentication failed');
      }
    }
  });

  // 監聽連接事件
  wss.on('connection', handleConnection);

  console.log('WebSocket 服務已啟動');
  return wss;
};

// 處理新的 WebSocket 連接
const handleConnection = async (ws, req) => {
  try {
    // 檢查是否為前端客戶端連接
    if (req.clientType === 'frontend') {
      console.log(`前端客戶端已連接: ${req.username}`);

      // 設置前端客戶端屬性
      ws.clientType = 'frontend';
      ws.userId = req.userId;
      ws.username = req.username;
      ws.isAlive = true;
      ws.connectionTime = Date.now();
      ws.lastActivityTime = Date.now();

      // 發送歡迎消息
      ws.send(JSON.stringify({
        type: 'welcome',
        message: '歡迎連接到EPD Manager WebSocket服務',
        timestamp: new Date().toISOString(),
        clientInfo: {
          userId: req.userId,
          username: req.username,
          clientType: 'frontend'
        },
        serverInfo: {
          time: new Date().toISOString(),
          version: '1.0.0'
        }
      }));

      // 設置心跳檢測
      ws.on('pong', () => {
        ws.isAlive = true;
        ws.lastActivityTime = Date.now();
      });

      // 處理接收到的消息
      ws.on('message', (message) => {
        ws.lastActivityTime = Date.now();
        handleMessage(ws, message);
      });

      // 處理關閉連接
      ws.on('close', (code, reason) => {
        console.log(`前端客戶端 ${req.username} 已斷開連接，代碼: ${code}, 原因: ${reason || '未提供'}`);

        // 清理設備狀態訂閱
        if (ws.subscribedStoreId) {
          unsubscribeDeviceStatus(ws, ws.subscribedStoreId);
        } else {
          unsubscribeDeviceStatus(ws);
        }

        // 清理網關狀態訂閱
        if (ws.subscribedGatewayStoreId) {
          unsubscribeGatewayStatus(ws, ws.subscribedGatewayStoreId);
        } else {
          unsubscribeGatewayStatus(ws);
        }

        // 清理門店資料訂閱
        if (ws.subscribedStoreDataStoreId) {
          unsubscribeStoreDataUpdate(ws, ws.subscribedStoreDataStoreId);
        } else {
          unsubscribeStoreDataUpdate(ws);
        }

        // 清理系統資料訂閱
        unsubscribeSystemDataUpdate(ws);

        // 清理模板訂閱
        if (ws.subscribedTemplateStoreId) {
          unsubscribeTemplateUpdate(ws, ws.subscribedTemplateStoreId);
        } else {
          unsubscribeTemplateUpdate(ws);
        }

        // 清理刷圖計畫訂閱
        if (ws.subscribedRefreshPlanStoreId) {
          unsubscribeRefreshPlanUpdate(ws, ws.subscribedRefreshPlanStoreId);
        }
      });

      // 處理錯誤
      ws.on('error', (error) => {
        console.error(`前端客戶端 ${req.username} WebSocket 錯誤:`, error);
      });

      return;
    }

    // 網關連接處理邏輯
    const { gatewayId, storeId, macAddress } = req;
    console.log(`網關 ${gatewayId} (門店: ${storeId}, MAC: ${macAddress}) 已連接`);

    // === 新增: 檢查並清理舊連接 ===
    const existingConnection = connectedGateways.get(gatewayId);
    if (existingConnection) {
      console.log(`檢測到網關 ${gatewayId} 的舊連接，正在清理...`);

      // 強制關閉舊連接
      try {
        existingConnection.terminate();
      } catch (error) {
        console.error(`關閉舊連接失敗:`, error);
      }

      // 記錄舊連接被替換的事件
      logGatewayEvent(gatewayId, 'connection-replaced', {
        reason: 'new_connection_established',
        oldConnectionTime: existingConnection.connectionTime,
        newConnectionTime: Date.now()
      });

      console.log(`已清理網關 ${gatewayId} 的舊連接`);
    }
    // === 新增結束 ===

    // 將 gatewayId、storeId 和 macAddress 附加到 WebSocket 實例
    ws.clientType = 'gateway';
    ws.gatewayId = gatewayId;
    ws.storeId = storeId;
    ws.macAddress = macAddress; // 添加MAC地址到WebSocket實例
    ws.isAlive = true;
    ws.connectionTime = Date.now();
    ws.lastActivityTime = Date.now(); // 記錄上次活動時間

    // 存儲連接
    connectedGateways.set(gatewayId, ws);

    // 更新網關狀態為在線
    await updateGatewayStatus(gatewayId, 'online');

    // 廣播網關連接狀態更新
    const gatewayUpdate = {
      _id: gatewayId,
      status: 'online',
      lastSeen: new Date().toISOString(),
      connectionInfo: {
        connectedAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        isWebSocketConnected: true
      },
      updatedFields: ['status', 'lastSeen', 'connectionInfo']
    };

    gatewayStatusBroadcaster.scheduleUpdate(storeId, [gatewayUpdate]);

    // 發送歡迎消息
    ws.send(JSON.stringify({
      type: 'welcome',
      message: 'WebSocket 連接成功',
      timestamp: Date.now(),
      gatewayInfo: {
        gatewayId: gatewayId,
        storeId: storeId,
        macAddress: macAddress // 在歡迎消息中包含MAC地址
      },
      serverInfo: {
        time: new Date().toISOString(),
        version: '1.0.0'
      }
    }));

    // 設置 ping/pong 心跳檢測
    ws.on('pong', () => {
      ws.isAlive = true;
      ws.lastActivityTime = Date.now(); // 更新最後活動時間
    });

    // 處理接收到的消息
    ws.on('message', (message) => {
      ws.lastActivityTime = Date.now(); // 更新最後活動時間
      handleMessage(ws, message);
    });

    // 處理關閉連接
    ws.on('close', async (code, reason) => {
      console.log(`網關 ${gatewayId} 已斷開連接，代碼: ${code}, 原因: ${reason || '未提供'}`);

      // === 修改: 檢查是否為當前活躍連接 ===
      const currentConnection = connectedGateways.get(gatewayId);
      if (currentConnection === ws) {
        // 只有當前連接才執行清理和狀態更新
        connectedGateways.delete(gatewayId);
        await updateGatewayStatus(gatewayId, 'offline');
        console.log(`已清理網關 ${gatewayId} 的連接並設為離線`);

        // 廣播網關離線狀態更新
        const gatewayUpdate = {
          _id: gatewayId,
          status: 'offline',
          lastSeen: new Date().toISOString(),
          connectionInfo: {
            isWebSocketConnected: false,
            disconnectedAt: new Date().toISOString()
          },
          updatedFields: ['status', 'lastSeen', 'connectionInfo']
        };

        gatewayStatusBroadcaster.scheduleUpdate(storeId, [gatewayUpdate]);
      } else {
        // 如果不是當前連接，說明已經有新連接替代了
        console.log(`網關 ${gatewayId} 的舊連接關閉，但已有新連接，不更新狀態`);
      }
      // === 修改結束 ===

      // 記錄網關離線事件
      logGatewayEvent(gatewayId, 'disconnect', { code, reason });
    });

    // 處理錯誤
    ws.on('error', async (error) => {
      console.error(`網關 ${gatewayId} WebSocket 錯誤:`, error);
      // 記錄網關錯誤事件
      logGatewayEvent(gatewayId, 'error', { message: error.message, stack: error.stack });
    });

    // 記錄網關連接事件
    logGatewayEvent(gatewayId, 'connect', { storeId });
  } catch (error) {
    console.error('處理 WebSocket 連接時發生錯誤:', error);
    ws.close(1011, 'Server error');
  }
};

// 更新網關狀態 (online/offline)
const updateGatewayStatus = async (gatewayId, status) => {
  try {
    const { collection } = await getGatewayCollection();

    await collection.updateOne(
      { _id: new ObjectId(gatewayId) },
      {
        $set: {
          status: status,
          lastSeen: new Date(),
          updatedAt: new Date()
        }
      }
    );

    console.log(`已更新網關 ${gatewayId} 狀態為 ${status}`);
  } catch (error) {
    console.error(`更新網關 ${gatewayId} 狀態失敗:`, error);
  }
};

// 處理 WebSocket 消息
const handleMessage = async (ws, message) => {
  try {
    const data = JSON.parse(message);
    const { type } = data;
    const gatewayId = ws.gatewayId;
    const storeId = ws.storeId;

    // 區分網關和前端客戶端消息
    if (ws.clientType === 'frontend') {
      console.log(`收到來自前端客戶端的消息類型: ${type}`);
      await handleFrontendMessage(ws, data);
      return;
    }

    console.log(`收到來自網關 ${gatewayId} 的消息類型: ${type}`);

    switch (type) {
      case 'ping':
        // 處理心跳消息
        handlePingMessage(ws, data);
        break;

      case 'deviceStatus':
        // 處理設備狀態更新消息
        await handleDeviceStatusMessage(ws, data);
        break;

      case 'gatewayInfo':
        // 處理網關信息更新
        await handleGatewayInfoMessage(ws, data);
        break;

      case 'chunk_start_ack':
        // 處理分片開始確認消息
        console.log(`收到分片開始確認: chunkId=${data.chunkId}, status=${data.status}`);
        // 這些ACK消息由分片傳輸的等待機制處理，這裡只需記錄
        break;

      case 'chunk_ack':
        // 處理分片確認消息
        console.log(`收到分片確認: chunkId=${data.chunkId}, chunkIndex=${data.chunkIndex}, status=${data.status}`);
        // 這些ACK消息由分片傳輸的等待機制處理，這裡只需記錄
        break;

      case 'chunk_complete_ack':
        // 處理分片完成確認消息
        console.log(`收到分片完成確認: chunkId=${data.chunkId}, status=${data.status}`);
        // 這些ACK消息由分片傳輸的等待機制處理，這裡只需記錄
        break;

      case 'requestPreviewImage':
        // 處理請求預覽圖像消息
        console.log(`收到預覽圖像請求: macAddress=${data.macAddress}`);
        // 這裡可以添加處理邏輯，目前只記錄
        break;

      default:
        console.warn(`收到未知類型的消息: ${type}`);
        ws.send(JSON.stringify({
          type: 'error',
          message: '未知的消息類型',
          originalType: type,
          timestamp: Date.now()
        }));
    }
  } catch (error) {
    console.error('處理 WebSocket 消息時發生錯誤:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: '處理消息時發生錯誤',
      error: error.message,
      timestamp: Date.now()
    }));
  }
};

// 處理前端客戶端消息
const handleFrontendMessage = async (ws, data) => {
  const { type } = data;

  switch (type) {
    case 'client_identify':
      // 處理客戶端識別
      ws.clientType = data.clientType || 'frontend';
      console.log(`前端客戶端已識別: ${ws.clientType}`);
      ws.send(JSON.stringify({
        type: 'welcome',
        message: '歡迎連接到EPD Manager WebSocket服務',
        timestamp: new Date().toISOString()
      }));
      break;

    case 'ping':
      // 處理前端心跳
      ws.send(JSON.stringify({
        type: 'pong',
        timestamp: data.timestamp,
        serverTime: new Date().toISOString()
      }));
      break;

    case 'subscribe_batch_progress':
      // 訂閱批量傳送進度
      if (data.batchId) {
        subscribeBatchProgress(data.batchId, ws);
        ws.send(JSON.stringify({
          type: 'subscription_ack',
          batchId: data.batchId,
          subscribed: true,
          timestamp: new Date().toISOString()
        }));
      }
      break;

    case 'unsubscribe_batch_progress':
      // 取消訂閱批量傳送進度
      if (data.batchId) {
        unsubscribeBatchProgress(data.batchId, ws);
        ws.send(JSON.stringify({
          type: 'subscription_ack',
          batchId: data.batchId,
          subscribed: false,
          timestamp: new Date().toISOString()
        }));
      }
      break;

    case 'subscribe_device_status':
      // 訂閱設備狀態
      subscribeDeviceStatus(ws, data.storeId, data.options);
      break;

    case 'unsubscribe_device_status':
      // 取消訂閱設備狀態
      unsubscribeDeviceStatus(ws, data.storeId);
      break;

    case 'subscribe_gateway_status':
      // 訂閱網關狀態
      subscribeGatewayStatus(ws, data.storeId, data.options);
      break;

    case 'unsubscribe_gateway_status':
      // 取消訂閱網關狀態
      unsubscribeGatewayStatus(ws, data.storeId);
      break;

    case 'cancel_batch_send':
      // 取消批量傳送
      if (data.batchId) {
        console.log(`收到取消批量傳送請求: ${data.batchId}`);
        // 這裡可以添加實際的取消邏輯
        ws.send(JSON.stringify({
          type: 'batch_cancel_ack',
          batchId: data.batchId,
          cancelled: true,
          timestamp: new Date().toISOString()
        }));
      }
      break;

    case 'subscribe_store_data_update':
      // 訂閱門店資料更新
      subscribeStoreDataUpdate(ws, data.storeId, data.options);
      ws.send(JSON.stringify({
        type: 'store_data_subscription_ack',
        storeId: data.storeId,
        subscribed: true,
        timestamp: new Date().toISOString()
      }));
      break;

    case 'unsubscribe_store_data_update':
      // 取消訂閱門店資料更新
      unsubscribeStoreDataUpdate(ws, data.storeId);
      ws.send(JSON.stringify({
        type: 'store_data_subscription_ack',
        storeId: data.storeId,
        subscribed: false,
        timestamp: new Date().toISOString()
      }));
      break;

    case 'subscribe_system_data_update':
      // 訂閱系統資料更新
      subscribeSystemDataUpdate(ws, data.options);
      ws.send(JSON.stringify({
        type: 'system_data_subscription_ack',
        subscribed: true,
        timestamp: new Date().toISOString()
      }));
      break;

    case 'unsubscribe_system_data_update':
      // 取消訂閱系統資料更新
      unsubscribeSystemDataUpdate(ws);
      ws.send(JSON.stringify({
        type: 'system_data_subscription_ack',
        subscribed: false,
        timestamp: new Date().toISOString()
      }));
      break;

    case 'subscribe_template_update':
      // 訂閱模板更新
      subscribeTemplateUpdate(ws, data.storeId, data.options);
      ws.send(JSON.stringify({
        type: 'template_subscription_ack',
        storeId: data.storeId,
        subscribed: true,
        timestamp: new Date().toISOString()
      }));
      break;

    case 'unsubscribe_template_update':
      // 取消訂閱模板更新
      unsubscribeTemplateUpdate(ws, data.storeId);
      ws.send(JSON.stringify({
        type: 'template_subscription_ack',
        storeId: data.storeId,
        subscribed: false,
        timestamp: new Date().toISOString()
      }));
      break;

    case 'subscribe_refresh_plan_update':
      // 訂閱刷圖計畫更新
      subscribeRefreshPlanUpdate(ws, data.storeId, data.options);
      break;

    case 'unsubscribe_refresh_plan_update':
      // 取消訂閱刷圖計畫更新
      unsubscribeRefreshPlanUpdate(ws, data.storeId);
      break;

    default:
      console.warn(`收到未知的前端消息類型: ${type}`);
      ws.send(JSON.stringify({
        type: 'error',
        message: '未知的消息類型',
        originalType: type,
        timestamp: new Date().toISOString()
      }));
  }
};

// 處理 ping 消息
const handlePingMessage = (ws, data) => {
  const response = {
    type: 'pong',
    timestamp: data.timestamp,
    serverTime: Date.now()
  };
  ws.send(JSON.stringify(response));
};

// 處理設備狀態消息
const handleDeviceStatusMessage = async (ws, data) => {
  try {
    const gatewayId = ws.gatewayId;
    const storeId = ws.storeId;
    const devices = data.devices || [];

    if (!Array.isArray(devices) || devices.length === 0) {
      throw new Error('設備列表為空或無效');
    }

    console.log(`處理來自網關 ${gatewayId} 的 ${devices.length} 個設備狀態更新`);

    // 處理每個設備的狀態更新
    const processedDevices = await updateDeviceStatus(gatewayId, devices, storeId);

    // 發送確認回應
    ws.send(JSON.stringify({
      type: 'deviceStatusAck',
      timestamp: Date.now(),
      success: true,
      message: '設備狀態更新成功',
      detailStatus: processedDevices.map(device => ({
        macAddress: device.macAddress,
        status: 'success',
        deviceId: device._id.toString()
      }))
    }));
  } catch (error) {
    console.error('處理設備狀態消息失敗:', error);
    ws.send(JSON.stringify({
      type: 'deviceStatusAck',
      timestamp: Date.now(),
      success: false,
      message: '處理設備狀態失敗: ' + error.message
    }));
  }
};

// 處理網關信息更新消息
const handleGatewayInfoMessage = async (ws, data) => {
  try {
    const gatewayId = ws.gatewayId;
    const tokenMacAddress = ws.macAddress; // 從WebSocket連接中獲取token中的MAC地址
    const info = data.info || {};

    if (!info || typeof info !== 'object') {
      throw new Error('網關信息無效');
    }

    console.log(`更新網關 ${gatewayId} 的信息`);

    // 處理Gateway能力上報
    if (info.chunkingSupport) {
      const chunkingSupport = info.chunkingSupport;
      gatewayCapabilities.set(tokenMacAddress, chunkingSupport);

      // 新增：處理偏好的 rawdata 格式
      if (chunkingSupport.supportedFormat && typeof chunkingSupport.supportedFormat === 'string') {
        const validFormats = [RAWDATA_FORMATS.RAWDATA, RAWDATA_FORMATS.RUNLENDATA];
        if (validFormats.includes(chunkingSupport.supportedFormat)) {
          gatewayPreferredFormats.set(tokenMacAddress, chunkingSupport.supportedFormat);
          console.log(`Gateway ${tokenMacAddress} 偏好 rawdata 格式: ${chunkingSupport.supportedFormat}`);
        } else {
          gatewayPreferredFormats.set(tokenMacAddress, RAWDATA_FORMATS.RAWDATA);
          console.log(`Gateway ${tokenMacAddress} 指定了無效格式 ${chunkingSupport.supportedFormat}，使用預設 rawdata`);
        }
      } else {
        // 預設使用 rawdata
        gatewayPreferredFormats.set(tokenMacAddress, RAWDATA_FORMATS.RAWDATA);
        console.log(`Gateway ${tokenMacAddress} 未指定格式偏好，使用預設 rawdata`);
      }

      console.log(`Gateway ${tokenMacAddress} 分片能力:`, {
        enabled: chunkingSupport.enabled,
        maxChunkSize: chunkingSupport.maxChunkSize,
        maxSingleMessageSize: chunkingSupport.maxSingleMessageSize,
        embeddedIndexSupport: chunkingSupport.embeddedIndex,
        preferredFormat: gatewayPreferredFormats.get(tokenMacAddress)
      });
    } else {
      console.log(`Gateway ${tokenMacAddress} 未上報分片能力，視為不支援`);
      gatewayPreferredFormats.set(tokenMacAddress, RAWDATA_FORMATS.RAWDATA);
    }

    // 更新網關信息
    const { collection } = await getGatewayCollection();

    // 確保網關存在
    const gateway = await collection.findOne({ _id: new ObjectId(gatewayId) });
    if (!gateway) {
      throw new Error('找不到指定的網關');
    }

    // 驗證網關發送的MAC地址與token中的MAC地址是否匹配
    if (info.macAddress && info.macAddress !== tokenMacAddress) {
      console.error(`安全警告: 網關 ${gatewayId} 發送的MAC地址 (${info.macAddress}) 與token中的MAC地址 (${tokenMacAddress}) 不匹配，強制中斷連線`);

      // 記錄安全事件
      await logGatewayEvent(gatewayId, 'security-violation', {
        type: 'mac_address_mismatch',
        reportedMac: info.macAddress,
        tokenMac: tokenMacAddress,
        clientIP: ws.clientIP || 'unknown',
        timestamp: new Date().toISOString()
      });

      // 發送錯誤回應後立即關閉連接
      try {
        ws.send(JSON.stringify({
          type: 'gatewayInfoAck',
          timestamp: Date.now(),
          success: false,
          message: 'MAC地址不匹配，連線已中斷',
          fatal: true
        }));
      } catch (sendError) {
        console.error('發送錯誤回應失敗:', sendError);
      }

      // 強制關閉連接
      setTimeout(() => {
        ws.terminate();
      }, 100); // 給一點時間讓錯誤消息發送出去

      return; // 直接返回，不繼續處理
    }

    // 更新網關信息
    await collection.updateOne(
      { _id: new ObjectId(gatewayId) },
      {
        $set: {
          name: info.name || gateway.name,
          model: info.model || gateway.model,
          wifiFirmwareVersion: info.wifiFirmwareVersion || gateway.wifiFirmwareVersion,
          btFirmwareVersion: info.btFirmwareVersion || gateway.btFirmwareVersion,
          ipAddress: info.ipAddress || gateway.ipAddress,
          lastSeen: new Date(),
          updatedAt: new Date()
        }
      }
    );

    // 發送確認回應，包含server能力信息
    ws.send(JSON.stringify({
      type: 'gatewayInfoAck',
      timestamp: Date.now(),
      success: true,
      message: '網關信息更新成功',
      serverCapabilities: {
        embeddedIndexChunking: true,
        maxImageSize: 1024 * 1024,    // Server 支援的最大圖片大小
        supportedFormats: ['rawdata']
      }
    }));

    // 廣播網關信息更新
    const gatewayUpdate = {
      _id: gatewayId,
      status: gateway.status, // 確保包含當前狀態
      name: info.name || gateway.name,
      model: info.model || gateway.model,
      wifiFirmwareVersion: info.wifiFirmwareVersion || gateway.wifiFirmwareVersion,
      btFirmwareVersion: info.btFirmwareVersion || gateway.btFirmwareVersion,
      ipAddress: info.ipAddress || gateway.ipAddress,
      lastSeen: new Date().toISOString(),
      updatedFields: ['name', 'model', 'wifiFirmwareVersion', 'btFirmwareVersion', 'ipAddress', 'lastSeen']
    };

    gatewayStatusBroadcaster.scheduleUpdate(ws.storeId, [gatewayUpdate]);
  } catch (error) {
    console.error('處理網關信息消息失敗:', error);
    ws.send(JSON.stringify({
      type: 'gatewayInfoAck',
      timestamp: Date.now(),
      success: false,
      message: '處理網關信息失敗: ' + error.message
    }));
  }
};



// 更新設備狀態
const updateDeviceStatus = async (gatewayId, devices, storeId) => {
  // 獲取數據庫連接
  const { collection: deviceCollection } = await getDeviceCollection();
  const { collection: gatewayCollection } = await getGatewayCollection();

  const processedDevices = [];
  const gatewayObjectId = new ObjectId(gatewayId);

  // 查詢網關信息
  const gateway = await gatewayCollection.findOne({ _id: gatewayObjectId });
  if (!gateway) {
    throw new Error(`網關 ${gatewayId} 不存在`);
  }

  for (const device of devices) {
    try {
      // 確保設備有有效的 MAC 地址
      if (!device.macAddress || !/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(device.macAddress)) {
        console.warn(`來自網關 ${gatewayId} 的無效設備 MAC 地址: ${device.macAddress}`);
        continue;
      }

      // 查找是否已存在相同MAC地址的設備
      const existingDevice = await deviceCollection.findOne({ macAddress: device.macAddress });      if (!existingDevice) {
        // 如果設備不存在，創建新設備並設置為已初始化狀態
        // 因為是通過WebSocket發現的設備，所以直接標記為已初始化

        // 處理 colorType 轉換
        let deviceData = { ...(device.data || {}) };

        // 從裝置回報的數據中移除 imageCode，避免將其寫入資料庫
        if (deviceData.imageCode !== undefined) {
          delete deviceData.imageCode;
          console.log(`已從新設備 ${device.macAddress} 回報的數據中移除 imageCode，防止寫入資料庫`);
        }

        if (deviceData.colorType !== undefined) {
          const convertedColorType = shortCodeToDisplayColorType(deviceData.colorType);
          deviceData.colorType = convertedColorType;
          console.log(`新設備 ${device.macAddress} 的顏色類型從 ${device.data?.colorType} 轉換為 ${convertedColorType}`);
        }

        const newDevice = {
          macAddress: device.macAddress,
          status: 'online',
          dataId: device.dataId || '',
          primaryGatewayId: gatewayObjectId,  // 將當前網關設置為主要網關
          initialized: true,  // 設為已初始化，因為是通過網關發現
          storeId: storeId,
          otherGateways: [],
          lastSeen: new Date(),
          note: device.note || '',
          data: {
            size: device.data?.size || '10.3',
            battery: device.data?.battery || 100,
            rssi: device.data?.rssi || -50,
            ...deviceData
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };
          // 獲取網關所在門店的預設管理員資訊，用於自動綁定設備
        try {          const { db } = await getDbConnection();
          const storeCollection = db.collection('stores');
          const gatewayCollection = db.collection('gateways');

          // 獲取網關記錄，以得到正確的門店ID
          const gateway = await gatewayCollection.findOne({ _id: new ObjectId(gatewayId) });

          if (!gateway) {
            throw new Error(`找不到ID為 "${gatewayId}" 的網關`);
          }

          const gatewayStoreId = gateway.storeId;
          console.log(`網關 ${gatewayId} 關聯的門店ID: ${gatewayStoreId}`);

          // 首先嘗試使用 id 字段查詢
          let store = await storeCollection.findOne({ id: gatewayStoreId });

          // 如果沒找到，嘗試使用 _id 字段查詢（如果 storeId 是有效的 ObjectId）
          if (!store && ObjectId.isValid(gatewayStoreId)) {
            store = await storeCollection.findOne({ _id: new ObjectId(gatewayStoreId) });
          }

          // 如果還是找不到，嘗試查詢所有門店，這是一個最後的手段
          if (!store) {
            console.warn(`通過網關的 storeId "${gatewayStoreId}" 找不到門店，嘗試使用參數中的 storeId "${storeId}"`);
            store = await storeCollection.findOne({ id: storeId });

            if (!store && ObjectId.isValid(storeId)) {
              store = await storeCollection.findOne({ _id: new ObjectId(storeId) });
            }
          }

          // 如果找到門店且有管理員，自動綁定給管理員
          console.log(`===== 設備自動綁定日誌 =====`);
          console.log(`門店資訊: ${JSON.stringify(store)}`);
          console.log(`門店ID: ${storeId}, 管理員ID存在檢查: ${Boolean(store && store.managerId)}`);

          if (store && store.managerId) {
            console.log(`正在將設備 ${device.macAddress} 自動綁定到管理員 ${store.managerId}`);
            newDevice.userId = new ObjectId(store.managerId);
            console.log(`綁定成功，設備 userId 已設置為: ${newDevice.userId}`);
          } else {
            console.warn(`無法綁定設備: 門店 ${storeId} 沒有指定管理員`);
          }
        } catch (storeError) {
          console.warn(`查詢網關所屬門店資訊失敗，設備暫不綁定用戶: ${storeError.message}`);
          // 繼續創建設備，但不綁定用戶
        }

        const result = await deviceCollection.insertOne(newDevice);
        const insertedDevice = { ...newDevice, _id: result.insertedId };
        processedDevices.push(insertedDevice);

        // 將設備ID添加到網關的設備列表中
        await gatewayCollection.updateOne(
          { _id: gatewayObjectId },
          { $addToSet: { devices: result.insertedId } }
        );

        // 記錄新設備發現事件
        await logDeviceEvent(result.insertedId, 'discovered', {
          gatewayId: gatewayId,
          storeId: storeId,
          initialStatus: 'online',
          macAddress: device.macAddress
        });
          // 如果自動綁定了用戶，記錄綁定事件
        if (newDevice.userId) {
          try {
            const { db } = await getDbConnection();
            const userCollection = db.collection('users');
            const user = await userCollection.findOne({ _id: newDevice.userId });

            await logDeviceEvent(result.insertedId, 'user_binding', {
              action: 'auto_bind',
              userId: newDevice.userId.toString(),
              userName: user ? (user.name || user.username || '未知') : '未知',
              userEmail: user ? (user.email || '未知') : '未知',
              note: '設備被網關發現時自動綁定到門店管理員'
            });
          } catch (eventError) {
            console.warn('記錄自動綁定事件失敗:', eventError);
            // 不中斷流程
          }
        }

        console.log(`網關 ${gatewayId} 發現新設備 ${device.macAddress}`);
        continue;
      }

      // 更新設備狀態和最後活動時間
      const updateData = {
        status: 'online',
        lastSeen: new Date(),
        updatedAt: new Date()
      };      // 更新設備數據
      if (device.data) {
        const existingData = existingDevice.data || {};
        const deviceData = { ...device.data };

        // 從裝置回報的數據中移除 imageCode，避免覆蓋資料庫中的值
        if (deviceData.imageCode !== undefined) {
          delete deviceData.imageCode;
          console.log(`已從裝置 ${device.macAddress} 回報的數據中移除 imageCode，防止覆蓋資料庫值`);
        }

        // 更新設備數據中的特定欄位，但不包含 imageCode
        updateData.data = {
          ...existingData,
          ...deviceData
        };

        // 處理 colorType 轉換
        if (device.data.colorType !== undefined) {
          const convertedColorType = shortCodeToDisplayColorType(device.data.colorType);
          updateData.data.colorType = convertedColorType;
          console.log(`設備 ${device.macAddress} 的顏色類型從 ${device.data.colorType} 轉換為 ${convertedColorType}`);
        }

        // 確保必要的欄位存在
        if (device.data.battery !== undefined) updateData.data.battery = device.data.battery;
        if (device.data.rssi !== undefined) updateData.data.rssi = device.data.rssi;
        if (device.data.size !== undefined) updateData.data.size = device.data.size;

        // 檢查裝置回報的 imageCode 與資料庫中儲存的 imageCode
        if (device.data.imageCode !== undefined) {
          // 檢查裝置是否有 imageCode 欄位
          // 注意：imageCode 存儲在 data.imageCode 中
          const deviceImageCode = existingDevice.data?.imageCode || '';
          // 比對回報的 imageCode 與資料庫中的 imageCode
          if (deviceImageCode && device.data.imageCode === deviceImageCode) {
            // 相同代表已更新
            updateData.imageUpdateStatus = '已更新';
            console.log(`設備 ${device.macAddress} 圖片已更新成功，imageCode: ${device.data.imageCode}`);
          } else if (deviceImageCode) {
            // 不同代表未更新
            updateData.imageUpdateStatus = '未更新';
            console.log(`設備 ${device.macAddress} 圖片未更新，期望: ${deviceImageCode}, 實際: ${device.data.imageCode}`);
          }
          console.log(`設備 ${device.macAddress} 的圖片更新狀態更新為: ${updateData.imageUpdateStatus || '未設置'}`);
        }
      }

        // 處理設備初始化和網關綁定邏輯
      if (!existingDevice.initialized) {
        // 設備首次被發現，將當前網關設為主要網關
        updateData.primaryGatewayId = gatewayObjectId;
        updateData.initialized = true;
        updateData.gatewaySelectionMode = 'manual'; // 默認為固定模式
        console.log(`設備 ${existingDevice._id} 已被網關 ${gatewayId} 初始化`);

        // 將設備ID添加到網關的設備列表中
        await gatewayCollection.updateOne(
          { _id: gatewayObjectId },
          { $addToSet: { devices: existingDevice._id } }
        );

        // 如果設備由API創建時綁定了用戶，則保留這個綁定
        // API創建的設備已經有用戶ID，我們只需更新初始化狀態和主要網關
      } else if (existingDevice.primaryGatewayId) {        // 如果設備處於自動網關選擇模式，進行主要網關的自動選擇
        if (existingDevice.gatewaySelectionMode === 'auto') {
          const oldPrimaryGatewayId = existingDevice.primaryGatewayId;
          let needSwitchGateway = false;
          let switchReason = '';

          // 檢查當前主要網關是否離線
          if (!isGatewayOnline(oldPrimaryGatewayId.toString())) {
            needSwitchGateway = true;
            switchReason = '主要網關離線';
          }
          // 檢查信號強度是否更好（只在主要網關在線時比較）
          else if (device.data && device.data.rssi !== undefined &&
                  existingDevice.data && existingDevice.data.rssi !== undefined &&
                  device.data.rssi > existingDevice.data.rssi + 15) {
            needSwitchGateway = true;
            switchReason = `信號強度更好 (${device.data.rssi} > ${existingDevice.data.rssi})`;
          }

          // 如果需要切換網關
          if (needSwitchGateway) {
            // 記錄主要網關切換事件
            await logDeviceEvent(existingDevice._id, 'gateway_changed', {
              action: 'auto_switch_gateway',
              oldPrimaryGatewayId: oldPrimaryGatewayId.toString(),
              newPrimaryGatewayId: gatewayId,
              oldRssi: existingDevice.data?.rssi,
              newRssi: device.data?.rssi,
              gatewayName: gateway.name || '未知',
              reason: switchReason
            });

            // 將舊主要網關添加到其他網關列表
            await deviceCollection.updateOne(
              { _id: existingDevice._id },
              { $addToSet: { otherGateways: oldPrimaryGatewayId } }
            );

            // 設置新的主要網關
            updateData.primaryGatewayId = gatewayObjectId;

            // 從其他網關列表中移除新主要網關
            await deviceCollection.updateOne(
              { _id: existingDevice._id },
              { $pull: { otherGateways: gatewayObjectId } }
            );

            console.log(`自動模式：設備 ${existingDevice._id} 的主要網關已從 ${oldPrimaryGatewayId} 切換到 ${gatewayId} (原因: ${switchReason})`);
          }
        }

        // 檢查當前網關是否是主要網關
        if (!existingDevice.primaryGatewayId.equals(gatewayObjectId)) {
          // 當前網關不是主要網關，應添加到其他網關列表
          // 確保 otherGateways 是一個數組
          const otherGateways = Array.isArray(existingDevice.otherGateways)
            ? existingDevice.otherGateways
            : [];

          // 檢查當前網關是否已在其他網關列表中
          // 使用更安全的方式來比較 ObjectId
          const isGatewayInList = otherGateways.some(id =>
            (id && id.equals && id.equals(gatewayObjectId)) ||
            (id && id.toString() === gatewayId)
          );          if (!isGatewayInList) {
            // 將設備ID添加到網關的設備列表中
            await gatewayCollection.updateOne(
              { _id: gatewayObjectId },
              { $addToSet: { devices: existingDevice._id } }
            );

            // 準備更新操作，添加當前網關到其他網關列表
            // 這裡需要分開使用 $set 和 $addToSet 操作符
            await deviceCollection.updateOne(
              { _id: existingDevice._id },
              {
                $set: updateData,
                $addToSet: { otherGateways: gatewayObjectId }
              }
            );

            console.log(`設備 ${existingDevice._id} 添加了新的發現網關 ${gatewayId}`);

            // 獲取更新後的設備，用於返回處理結果
            const updatedDevice = await deviceCollection.findOne({ _id: existingDevice._id });
            processedDevices.push(updatedDevice);
            continue;  // 繼續處理下一個設備
          }
        }
      } else {
        // 如果沒有主要網關（這種情況不應該發生，但為了穩健性處理一下）
        updateData.primaryGatewayId = gatewayObjectId;
        console.log(`設備 ${existingDevice._id} 設置主要網關為 ${gatewayId}`);
      }
        // 只執行標準更新 ($set 操作)，沒有添加到 otherGateways
      await deviceCollection.updateOne(
        { _id: existingDevice._id },
        { $set: updateData }
      );

      // 獲取更新後的設備，用於返回處理結果
      const updatedDevice = await deviceCollection.findOne({ _id: existingDevice._id });
      processedDevices.push(updatedDevice);
    } catch (deviceError) {
      console.error(`處理設備 ${device.macAddress} 狀態時發生錯誤:`, deviceError);
      // 繼續處理下一個設備，而不是中斷整個流程
    }
  }

  // 廣播設備狀態更新
  if (processedDevices.length > 0) {
    const deviceUpdates = processedDevices.map(device => ({
      _id: device._id.toString(),
      macAddress: device.macAddress,
      status: device.status,
      lastSeen: device.lastSeen.toISOString(),
      imageUpdateStatus: device.imageUpdateStatus,
      data: {
        size: device.data?.size,
        battery: device.data?.battery,
        rssi: device.data?.rssi,
        colorType: device.data?.colorType,
        imageCode: device.data?.imageCode
      },
      updatedFields: ['status', 'lastSeen', 'data']
    }));

    deviceStatusBroadcaster.scheduleUpdate(storeId, deviceUpdates);
  }

  return processedDevices;
};

// 記錄設備事件 (如狀態變更、數據更新等)
const logDeviceEvent = async (deviceId, eventType, eventData = {}) => {
  try {
    if (!getDbConnection) {
      console.warn(`無法記錄設備事件: 數據庫連接未初始化`);
      return;
    }

    // 驗證 deviceId 是否有效
    if (!deviceId || !(deviceId instanceof ObjectId || typeof deviceId === 'string')) {
      console.error('嘗試記錄設備事件時提供了無效的設備ID');
      return;
    }

    // 確保 deviceId 是 ObjectId 類型
    const deviceObjectId = deviceId instanceof ObjectId ? deviceId : new ObjectId(deviceId);

    const { db } = await getDbConnection();

    // 確保 deviceEvents 集合存在
    const collections = await db.listCollections({ name: 'deviceEvents' }).toArray();
    if (collections.length === 0) {
      await db.createCollection('deviceEvents');
      // 創建索引以提高查詢效率
      await db.collection('deviceEvents').createIndex({ deviceId: 1 });
      await db.collection('deviceEvents').createIndex({ timestamp: -1 });
      await db.collection('deviceEvents').createIndex({ eventType: 1 });
    }

    const eventCollection = db.collection('deviceEvents');

    // 獲取設備詳情以豐富事件數據
    const deviceCollection = db.collection('devices');
    const device = await deviceCollection.findOne({ _id: deviceObjectId });

    if (!device) {
      console.warn(`找不到ID為 ${deviceId} 的設備，但仍將記錄事件`);
    }

    // 創建標準化的事件對象
    const event = {
      deviceId: deviceObjectId,
      eventType, // 'online', 'offline', 'data_update', 'binding_changed', 'gateway_changed' 等
      eventData,
      timestamp: new Date(),
      deviceMac: device?.macAddress || 'unknown',
      storeId: device?.storeId || null
    };

    // 寫入事件紀錄
    await eventCollection.insertOne(event);

    console.log(`已記錄設備 ${deviceId} 的 ${eventType} 事件`);

    // 定期清理舊事件（保留90天的設備事件紀錄）
    if (Math.random() < 0.005) { // 0.5% 的概率執行清理，避免每次都執行
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

      eventCollection.deleteMany({ timestamp: { $lt: ninetyDaysAgo } })
        .then(result => {
          if (result.deletedCount > 0) {
            console.log(`已清理 ${result.deletedCount} 條過期的設備事件紀錄`);
          }
        })
        .catch(cleanupError => {
          console.error('清理舊設備事件紀錄失敗:', cleanupError);
        });
    }

    return event;
  } catch (error) {
    console.error(`記錄設備 ${deviceId} 事件失敗:`, error);
    return null;
  }
};

// 定期檢查連接狀態
const setupHeartbeatCheck = () => {
  const pingInterval = 30000; // 30 秒
  const terminateThreshold = 60000; // 60 秒沒有回應則斷開連接

  const interval = setInterval(() => {
    const now = Date.now();

    wss.clients.forEach((ws) => {
      // 檢查心跳超時
      if (ws.isAlive === false) {
        console.log(`網關 ${ws.gatewayId} 心跳檢測失敗，關閉連接`);
        // 記錄心跳檢測失敗事件
        logGatewayEvent(ws.gatewayId, 'heartbeat-fail', {
          lastActivityTime: new Date(ws.lastActivityTime).toISOString(),
          inactiveForMs: now - ws.lastActivityTime
        });
        return ws.terminate();
      }

      // 檢查長時間無活動
      if (now - ws.lastActivityTime > terminateThreshold) {
        console.log(`網關 ${ws.gatewayId} 長時間無活動 (${(now - ws.lastActivityTime) / 1000}秒)，關閉連接`);
        // 記錄無活動事件
        logGatewayEvent(ws.gatewayId, 'inactivity-timeout', {
          lastActivityTime: new Date(ws.lastActivityTime).toISOString(),
          inactiveForMs: now - ws.lastActivityTime
        });
        return ws.terminate();
      }

      // 重置心跳標誌並發送 ping
      ws.isAlive = false;
      try {
        ws.ping();
      } catch (error) {
        console.error(`向網關 ${ws.gatewayId} 發送 ping 失敗:`, error);
      }
    });
  }, pingInterval);

  // 當 WebSocket 服務關閉時，清除定時器
  wss.on('close', () => {
    clearInterval(interval);
  });

  console.log(`心跳檢測已啟動，間隔: ${pingInterval}ms，超時閾值: ${terminateThreshold}ms`);
};

// 記錄網關事件
const logGatewayEvent = async (gatewayId, eventType, eventData = {}) => {
  try {
    if (!getDbConnection) {
      console.warn(`無法記錄網關事件: 數據庫連接未初始化`);
      return;
    }

    // 驗證 gatewayId 是否有效
    if (!gatewayId || typeof gatewayId !== 'string') {
      console.error('嘗試記錄網關事件時提供了無效的網關ID');
      return;
    }

    const { db } = await getDbConnection();

    // 確保 gatewayEvents 集合存在
    const collections = await db.listCollections({ name: 'gatewayEvents' }).toArray();
    if (collections.length === 0) {
      await db.createCollection('gatewayEvents');
      // 創建索引以提高查詢效率
      await db.collection('gatewayEvents').createIndex({ gatewayId: 1 });
      await db.collection('gatewayEvents').createIndex({ timestamp: -1 });
      await db.collection('gatewayEvents').createIndex({ eventType: 1 });
    }

    const eventCollection = db.collection('gatewayEvents');

    // 創建標準化的事件對象
    const event = {
      gatewayId: new ObjectId(gatewayId),
      eventType, // 'connect', 'disconnect', 'error', 'heartbeat-fail', 'inactivity-timeout' 等
      eventData,
      timestamp: new Date(),
      storeId: null, // 稍後將被更新
      gatewayName: null // 稍後將被更新
    };

    // 嘗試獲取網關相關信息以豐富事件數據
    try {
      const gateway = await db.collection('gateways').findOne({ _id: new ObjectId(gatewayId) });
      if (gateway) {
        event.storeId = gateway.storeId;
        event.gatewayName = gateway.name;
      }
    } catch (gatewayLookupError) {
      console.warn(`獲取網關 ${gatewayId} 的額外信息失敗:`, gatewayLookupError.message);
    }

    // 寫入事件紀錄
    await eventCollection.insertOne(event);

    // 對於嚴重事件，增加額外的日誌輸出
    if (['error', 'heartbeat-fail', 'inactivity-timeout', 'security-violation'].includes(eventType)) {
      console.warn(`網關 ${gatewayId} ${event.gatewayName ? `(${event.gatewayName})` : ''} 發生 ${eventType} 事件: `,
                  JSON.stringify(eventData));
    } else {
      console.log(`已記錄網關 ${gatewayId} 的 ${eventType} 事件`);
    }

    // 定期清理舊事件（保留30天的事件紀錄）
    if (Math.random() < 0.01) { // 1% 的概率執行清理，避免每次都執行
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      eventCollection.deleteMany({ timestamp: { $lt: thirtyDaysAgo } })
        .then(result => {
          if (result.deletedCount > 0) {
            console.log(`已清理 ${result.deletedCount} 條過期的網關事件紀錄`);
          }
        })
        .catch(cleanupError => {
          console.error('清理舊網關事件紀錄失敗:', cleanupError);
        });
    }
  } catch (error) {
    console.error(`記錄網關 ${gatewayId} 事件失敗:`, error);
  }
};

// 獲取網關連接統計信息
const getGatewayConnectionStats = async () => {
  try {
    const stats = {
      totalConnected: connectedGateways.size,
      connectedGateways: [],
      lastUpdated: new Date()
    };

    // 獲取每個網關的連接詳情
    if (connectedGateways.size > 0) {
      const { collection } = await getGatewayCollection();

      for (const [gatewayId, ws] of connectedGateways.entries()) {
        try {
          const gateway = await collection.findOne({ _id: new ObjectId(gatewayId) });

          if (gateway) {
            stats.connectedGateways.push({
              id: gatewayId,
              name: gateway.name || 'Unknown',
              storeId: gateway.storeId,
              ipAddress: gateway.ipAddress,
              connectionTime: new Date(ws.connectionTime),
              lastActivity: new Date(ws.lastActivityTime),
              inactiveForMs: Date.now() - ws.lastActivityTime
            });
          } else {
            stats.connectedGateways.push({
              id: gatewayId,
              name: 'Unknown',
              connectionTime: new Date(ws.connectionTime),
              lastActivity: new Date(ws.lastActivityTime),
              inactiveForMs: Date.now() - ws.lastActivityTime
            });
          }
        } catch (error) {
          console.error(`獲取網關 ${gatewayId} 的連接統計信息失敗:`, error);
        }
      }
    }

    return stats;
  } catch (error) {
    console.error('獲取網關連接統計信息失敗:', error);
    return { error: error.message, totalConnected: 0, connectedGateways: [] };
  }
};

// 檢查特定網關是否在線
const isGatewayOnline = (gatewayId) => {
  return connectedGateways.has(gatewayId);
};

// ==================== Chunk傳輸狀態管理函數 ====================

// 檢查網關是否正在進行chunk傳輸
const isGatewayBusyWithChunk = (gatewayId) => {
  const gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
  if (!gatewayTransmissions || gatewayTransmissions.size === 0) {
    return false;
  }

  // 檢查是否有任何活躍的傳輸
  const now = Date.now();
  const timeout = 5 * 60 * 1000; // 5分鐘
  let hasActiveTransmission = false;

  // 檢查所有傳輸，清理超時的
  for (const [chunkId, transmission] of gatewayTransmissions) {
    if (now - transmission.startTime > timeout) {
      console.warn(`網關 ${gatewayId} 的chunk傳輸 ${chunkId} 已超時，清理狀態`);
      gatewayTransmissions.delete(chunkId);
    } else if (transmission.status === 'active') {
      hasActiveTransmission = true;
    }
  }

  // 如果沒有任何傳輸了，清理網關記錄
  if (gatewayTransmissions.size === 0) {
    activeChunkTransmissions.delete(gatewayId);
  }

  return hasActiveTransmission;
};

// 開始chunk傳輸狀態追蹤
const startChunkTransmission = (gatewayId, chunkId, deviceMac) => {
  const transmission = {
    chunkId,
    deviceMac,
    startTime: Date.now(),
    status: 'active'
  };

  // 獲取或創建網關的傳輸映射
  let gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
  const wasIdle = !gatewayTransmissions || gatewayTransmissions.size === 0;

  if (!gatewayTransmissions) {
    gatewayTransmissions = new Map();
    activeChunkTransmissions.set(gatewayId, gatewayTransmissions);
  }

  // 添加新的傳輸
  gatewayTransmissions.set(chunkId, transmission);
  console.log(`開始追蹤網關 ${gatewayId} 的chunk傳輸: ${chunkId} (當前活躍傳輸數: ${gatewayTransmissions.size})`);

  // 如果網關從閒置變為忙碌，廣播統計信息更新
  if (wasIdle && isGatewayOnline(gatewayId)) {
    console.log(`🔔 網關 ${gatewayId} 從閒置變為忙碌，廣播統計信息更新`);
    broadcastGatewayStatsUpdate(gatewayId);
  }
};

// 結束chunk傳輸狀態追蹤
const endChunkTransmission = (gatewayId, chunkId) => {
  const gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
  if (gatewayTransmissions && gatewayTransmissions.has(chunkId)) {
    const wasBusy = gatewayTransmissions.size > 0;
    gatewayTransmissions.delete(chunkId);
    console.log(`結束追蹤網關 ${gatewayId} 的chunk傳輸: ${chunkId} (剩餘活躍傳輸數: ${gatewayTransmissions.size})`);

    // 如果沒有任何傳輸了，清理網關記錄並發射事件
    if (gatewayTransmissions.size === 0) {
      activeChunkTransmissions.delete(gatewayId);
      console.log(`網關 ${gatewayId} 所有chunk傳輸已完成，清理網關記錄`);

      // 如果網關從忙碌變為閒置，發射狀態變化事件並廣播統計信息更新
      if (wasBusy && isGatewayOnline(gatewayId)) {
        console.log(`🔔 網關 ${gatewayId} 從忙碌變為閒置，發射狀態變化事件並廣播統計信息更新`);
        gatewayStatusEmitter.emit('gatewayAvailable', gatewayId);
        broadcastGatewayStatsUpdate(gatewayId);
      }
    }
  }
};

// 獲取所有空閒的網關（在線且不忙碌）
const getAvailableGateways = (gatewayIds) => {
  return gatewayIds.filter(gatewayId => {
    const isOnline = isGatewayOnline(gatewayId);
    const isBusy = isGatewayBusyWithChunk(gatewayId);

    // 調試信息
    if (isOnline) {
      const gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
      const activeCount = gatewayTransmissions ? gatewayTransmissions.size : 0;
      console.log(`🔍 網關 ${gatewayId} 狀態檢查: 在線=${isOnline}, 忙碌=${isBusy}, 活躍傳輸數=${activeCount}`);
    }

    return isOnline && !isBusy;
  });
};

// 廣播網關統計信息更新
const broadcastGatewayStatsUpdate = async (gatewayId) => {
  try {
    // 獲取網關信息以確定門店ID
    const { collection } = await getGatewayCollection();
    const gateway = await collection.findOne({ _id: new ObjectId(gatewayId) });

    if (!gateway) {
      console.warn(`廣播統計信息更新失敗：找不到網關 ${gatewayId}`);
      return;
    }

    const storeId = gateway.storeId;

    // 獲取該門店的訂閱者
    const subscribers = getGatewayStatusSubscribers(storeId);

    if (subscribers.size === 0) {
      console.log(`門店 ${storeId} 沒有網關狀態訂閱者，跳過統計信息更新廣播`);
      return;
    }

    // 獲取最新的統計信息
    const stats = await getGatewayConnectionStats();

    // 過濾該門店的網關
    const storeGateways = await collection.find({ storeId }).toArray();
    const storeGatewayIds = storeGateways.map(g => g._id.toString());

    // 過濾連接的網關
    stats.connectedGateways = stats.connectedGateways.filter(g =>
      storeGatewayIds.includes(g.id)
    );

    // 重新計算總數
    stats.totalConnected = stats.connectedGateways.length;

    // 計算忙碌/閒置統計
    let busyCount = 0;
    let idleCount = 0;
    const busyGatewayDetails = [];
    const idleGatewayDetails = [];

    stats.connectedGateways.forEach(gateway => {
      const isBusy = isGatewayBusyWithChunk(gateway.id);

      if (isBusy) {
        busyCount++;
        busyGatewayDetails.push({
          id: gateway.id,
          name: gateway.name,
          storeId: gateway.storeId,
          ipAddress: gateway.ipAddress,
          activeTransmissions: getGatewayTransmissionStatus(gateway.id)
        });
      } else {
        idleCount++;
        idleGatewayDetails.push({
          id: gateway.id,
          name: gateway.name,
          storeId: gateway.storeId,
          ipAddress: gateway.ipAddress
        });
      }
    });

    // 添加詳細的忙碌/閒置統計信息
    stats.busyGateways = busyCount;
    stats.idleGateways = idleCount;
    stats.busyGatewayDetails = busyGatewayDetails;
    stats.idleGatewayDetails = idleGatewayDetails;

    // 添加統計摘要
    stats.summary = {
      totalConnected: stats.totalConnected,
      busyCount,
      idleCount,
      busyPercentage: stats.totalConnected > 0 ? Math.round((busyCount / stats.totalConnected) * 100) : 0,
      idlePercentage: stats.totalConnected > 0 ? Math.round((idleCount / stats.totalConnected) * 100) : 0
    };

    // 構建統計信息更新事件
    const event = {
      type: 'gateway_stats_update',
      storeId,
      stats,
      timestamp: new Date().toISOString(),
      triggerGatewayId: gatewayId
    };

    console.log(`📊 廣播網關統計信息更新到門店 ${storeId}: 總連接=${stats.totalConnected}, 忙碌=${busyCount}, 閒置=${idleCount}, 訂閱者=${subscribers.size}`);

    // 向所有訂閱者廣播統計信息更新
    subscribers.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify(event));
        } catch (error) {
          console.error('廣播網關統計信息更新失敗:', error);
        }
      }
    });

  } catch (error) {
    console.error(`廣播網關統計信息更新失敗 (網關 ${gatewayId}):`, error);
  }
};

// 獲取網關的詳細傳輸狀態（調試用）
const getGatewayTransmissionStatus = (gatewayId) => {
  const gatewayTransmissions = activeChunkTransmissions.get(gatewayId);
  if (!gatewayTransmissions) {
    return { active: false, transmissions: [] };
  }

  const transmissions = [];
  for (const [chunkId, transmission] of gatewayTransmissions) {
    transmissions.push({
      chunkId,
      deviceMac: transmission.deviceMac,
      startTime: transmission.startTime,
      duration: Date.now() - transmission.startTime,
      status: transmission.status
    });
  }

  return {
    active: transmissions.length > 0,
    count: transmissions.length,
    transmissions
  };
};

// 向特定網關發送命令
const sendCommandToGateway = async (gatewayId, command) => {
  try {
    const ws = connectedGateways.get(gatewayId);
    if (!ws) {
      throw new Error(`網關 ${gatewayId} 不在線`);
    }

    let message;

    // 檢查命令是否為對象類型，如果是，則直接使用該對象
    if (typeof command === 'object' && command !== null) {
      // 如果命令對象已經有type屬性，則直接使用
      if (!command.type) {
        // 否則，設置為默認的command類型
        command.type = 'command';
      }

      // 確保有時間戳
      if (!command.timestamp) {
        command.timestamp = Date.now();
      }

      message = command;
    } else {
      // 如果命令不是對象，則使用舊的格式
      message = {
        type: 'command',
        command,
        timestamp: Date.now()
      };
    }

    // 檢查 JSON 訊息大小
    const gatewayMac = ws.macAddress;
    if (gatewayMac) {
      const sizeCheck = checkJsonMessageSize(message, gatewayMac);
      if (!sizeCheck.canSend) {
        console.log('📝 記錄因 JSON 訊息過大而被拒絕的任務');

        // 記錄被拒絕的任務到 gateway 事件
        try {
          await logGatewayEvent(gatewayId, 'message_rejected', {
            reason: 'exceeds_max_single_message_size',
            messageType: message.type,
            messageSize: sizeCheck.messageSize,
            maxSingleMessageSize: sizeCheck.maxSingleMessageSize,
            timestamp: new Date().toISOString(),
            errorMessage: sizeCheck.error
          });
          console.log(`已記錄網關 ${gatewayId} 的訊息被拒絕事件`);
        } catch (logError) {
          console.error('記錄被拒絕任務失敗:', logError);
        }

        throw new Error(sizeCheck.error);
      }
    }

    ws.send(JSON.stringify(message));
    console.log(`已發送命令到網關 ${gatewayId}: ${JSON.stringify(message).substring(0, 100)}...`);

    return { success: true, message: `命令已發送到網關 ${gatewayId}` };
  } catch (error) {
    console.error(`發送命令到網關 ${gatewayId} 失敗:`, error);
    return { success: false, error: error.message };
  }
};

// ==================== JSON 訊息大小檢查函數 ====================

// 檢查 JSON 訊息大小是否超過 Gateway 限制
const checkJsonMessageSize = (message, macAddress) => {
  const chunkingSupport = gatewayCapabilities.get(macAddress);

  if (!chunkingSupport || !chunkingSupport.maxSingleMessageSize) {
    // 如果沒有設定限制，則允許發送
    return { canSend: true };
  }

  const maxSingleMessageSize = chunkingSupport.maxSingleMessageSize;
  const messageStr = JSON.stringify(message);
  const messageSize = Buffer.byteLength(messageStr, 'utf8');

  console.log(`檢查 JSON 訊息大小: ${messageSize} bytes, Gateway 限制: ${maxSingleMessageSize} bytes`);

  if (messageSize > maxSingleMessageSize) {
    console.warn(`❌ JSON 訊息大小 ${messageSize} bytes 超過 Gateway ${macAddress} 的限制 ${maxSingleMessageSize} bytes，拒絕發送`);
    return {
      canSend: false,
      messageSize,
      maxSingleMessageSize,
      error: `JSON message size ${messageSize} exceeds maxSingleMessageSize ${maxSingleMessageSize} for Gateway ${macAddress}`
    };
  }

  console.log(`✅ JSON 訊息大小檢查通過`);
  return { canSend: true, messageSize };
};

// 安全發送 JSON 訊息到 Gateway（會檢查大小限制）
const safeSendJsonToGateway = async (ws, message, context = {}) => {
  const gatewayMac = ws.macAddress;
  const gatewayId = ws.gatewayId;

  if (gatewayMac) {
    const sizeCheck = checkJsonMessageSize(message, gatewayMac);
    if (!sizeCheck.canSend) {
      console.log(`📝 記錄因 ${message.type || 'unknown'} JSON 訊息過大而被拒絕的任務`);

      // 記錄被拒絕的任務到 gateway 事件
      try {
        if (gatewayId) {
          await logGatewayEvent(gatewayId, 'message_rejected', {
            reason: 'json_message_exceeds_max_single_message_size',
            messageType: message.type || 'unknown',
            messageSize: sizeCheck.messageSize,
            maxSingleMessageSize: sizeCheck.maxSingleMessageSize,
            context: context,
            timestamp: new Date().toISOString(),
            errorMessage: sizeCheck.error
          });
          console.log(`已記錄網關 ${gatewayId} 的 ${message.type || 'unknown'} 訊息被拒絕事件`);
        }
      } catch (logError) {
        console.error('記錄被拒絕任務失敗:', logError);
      }

      throw new Error(sizeCheck.error);
    }
  }

  ws.send(JSON.stringify(message));
  return { success: true, messageSize: sizeCheck?.messageSize };
};

// ==================== 分片傳輸相關函數 ====================

// 根據Gateway能力判斷是否啟用分片
const shouldUseChunking = (dataSize, macAddress, rawBuffer = null, imageData = null, dataType = 'rawdata') => {
  const chunkingSupport = gatewayCapabilities.get(macAddress);

  if (!chunkingSupport) {
    console.warn(`Gateway ${macAddress} 分片能力未知，使用保守設定`);
    return dataSize > 512; // 保守的 512 bytes 門檻
  }

  const maxChunkSize = chunkingSupport.maxChunkSize || 200;
  const chunkingEnabled = chunkingSupport.enabled || false;

  console.log(`判斷分片需求: 資料=${dataSize} bytes, Gateway maxChunkSize=${maxChunkSize} bytes`);

  // 如果資料大小超過 Gateway 的 maxChunkSize，且 Gateway 支援分片
  if (dataSize > maxChunkSize && chunkingEnabled) {
    console.log('✅ 啟用分片傳輸 (rawdata 超過 maxChunkSize)');
    return true;
  } else if (dataSize > maxChunkSize && !chunkingEnabled) {
    console.error('❌ 資料過大但 Gateway 不支援分片');
    throw new Error(`Data too large for Gateway ${macAddress}`);
  } else {
    // rawdata 大小 <= maxChunkSize，但需要檢查 JSON 訊息大小是否超過 maxSingleMessageSize
    if (rawBuffer && chunkingEnabled && chunkingSupport.maxSingleMessageSize) {
      // 構建直接傳輸的 JSON 訊息來檢查大小
      const directMessage = {
        type: 'update_preview',
        deviceMac: 'test', // 使用測試值
        imageCode: 'test',
        rawdata: Array.from(rawBuffer),
        dataType: dataType,
        timestamp: new Date().toISOString()
      };

      // 如果提供了 imageData，也加入檢查
      if (imageData) {
        directMessage.imageData = imageData;
      }

      const messageStr = JSON.stringify(directMessage);
      const messageSize = Buffer.byteLength(messageStr, 'utf8');
      const maxSingleMessageSize = chunkingSupport.maxSingleMessageSize;

      console.log(`檢查 JSON 訊息大小: ${messageSize} bytes, maxSingleMessageSize: ${maxSingleMessageSize} bytes`);

      if (messageSize > maxSingleMessageSize) {
        console.log('✅ 啟用分片傳輸 (JSON 訊息超過 maxSingleMessageSize)');
        return true;
      }
    }

    console.log('✅ 直接傳輸');
    return false;
  }
};

// 分片傳輸配置
const CHUNK_CONFIG = {
  MIN_CHUNK_SIZE: 4,                // 4 bytes 最小分片大小（支援極小硬體限制）
  MAX_CHUNK_SIZE: 512 * 1024,       // 512KB 最大分片大小
  DEFAULT_CHUNK_SIZE: 200,          // 預設分片大小
  PERFORMANCE_WARNING_THRESHOLD: 100 // 分片數量警告門檻
};

// 獲取分片大小
const getChunkSize = (macAddress) => {
  const chunkingSupport = gatewayCapabilities.get(macAddress);

  if (!chunkingSupport?.enabled) {
    throw new Error(`Gateway ${macAddress} 不支援分片傳輸`);
  }

  const requestedChunkSize = chunkingSupport.maxChunkSize || CHUNK_CONFIG.DEFAULT_CHUNK_SIZE;

  // 確保分片大小在合理範圍內（支援硬體限制的極小分片）
  const validatedChunkSize = Math.max(
    CHUNK_CONFIG.MIN_CHUNK_SIZE,
    Math.min(requestedChunkSize, CHUNK_CONFIG.MAX_CHUNK_SIZE)
  );

  // 如果調整了分片大小，記錄警告
  if (validatedChunkSize !== requestedChunkSize) {
    console.warn(`Gateway ${macAddress} 請求的分片大小 ${requestedChunkSize} 已調整為 ${validatedChunkSize} (範圍: ${CHUNK_CONFIG.MIN_CHUNK_SIZE}-${CHUNK_CONFIG.MAX_CHUNK_SIZE})`);
  }

  return validatedChunkSize;
};

// 檢查分片數量並發出性能警告
const checkChunkPerformance = (dataSize, chunkSize, macAddress) => {
  const totalChunks = Math.ceil(dataSize / chunkSize);

  if (totalChunks > CHUNK_CONFIG.PERFORMANCE_WARNING_THRESHOLD) {
    console.warn(`⚠️ Gateway ${macAddress} 分片數量過多: ${totalChunks} 個分片 (數據: ${dataSize} bytes, 分片大小: ${chunkSize} bytes)`);
    console.warn(`   這可能導致性能問題，建議增加 maxChunkSize 或減少數據大小`);
    console.warn(`   預計網絡消息數量: ${totalChunks * 2} 個 (分片 + ACK)`);
  }

  return totalChunks;
};

// 生成唯一的chunkId
const generateChunkId = () => {
  return `chunk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 計算校驗碼
const calculateChecksum = (data) => {
  let checksum = 0;
  for (let i = 0; i < data.length; i++) {
    checksum = (checksum + data[i]) % 65536;
  }
  return checksum.toString(16).padStart(4, '0');
};

// 嵌入式 Index 分片傳輸實作
const sendChunkedRawdataWithEmbeddedIndex = async (ws, deviceMac, imageCode, rawBuffer, chunkSize, dataType = 'rawdata') => {
  const INDEX_SIZE = 4;   // chunkIndex 佔用 4 bytes
  const chunkId = generateChunkId();
  const totalChunks = Math.ceil(rawBuffer.length / chunkSize);
  const gatewayId = ws.gatewayId;

  // 開始chunk傳輸狀態追蹤
  startChunkTransmission(gatewayId, chunkId, deviceMac);

  try {
    // 檢查分片性能並發出警告
    const gatewayMac = ws.macAddress;
    checkChunkPerformance(rawBuffer.length, chunkSize, gatewayMac);

    console.log(`分片配置: 分片大小=${chunkSize} bytes, 總分片數=${totalChunks}`);
    console.log(`數據大小: ${rawBuffer.length} bytes, 預計網絡消息: ${totalChunks * 2} 個`);

  // 1. 發送分片開始訊息（只發送一次）
  const chunkStartMessage = {
    type: 'image_chunk_start',
    chunkId: chunkId,
    deviceMac: deviceMac,
    imageCode: imageCode,
    totalChunks: totalChunks,
    totalSize: rawBuffer.length,
    chunkSize: chunkSize,        // 實際數據大小
    indexSize: INDEX_SIZE,        // index 佔用大小
    dataType: dataType,          // 使用 dataType 來傳送格式信息
    mode: 'embedded_index',       // 標識使用嵌入式 index 模式
    timestamp: new Date().toISOString()
  };
  ws.send(JSON.stringify(chunkStartMessage));

  // 等待開始確認
  await waitForStartAck(ws, chunkId);

  // 2. 順序發送每個分片（嵌入 index）
  for (let i = 0; i < totalChunks; i++) {
    const start = i * chunkSize;
    const end = Math.min(start + chunkSize, rawBuffer.length);
    const chunkData = rawBuffer.slice(start, end);

    // 創建包含 index 的完整分片
    const fullChunk = new Uint8Array(INDEX_SIZE + chunkData.length);

    // 前 4 bytes：chunkIndex (little-endian)
    const indexView = new DataView(fullChunk.buffer, 0, INDEX_SIZE);
    indexView.setUint32(0, i, true); // little-endian

    // 後續 bytes：實際數據
    fullChunk.set(chunkData, INDEX_SIZE);

    console.log(`發送分片 ${i}/${totalChunks}: ${fullChunk.length} bytes (${chunkData.length} + 4 index)`);

    // 使用重傳機制發送分片
    await sendEmbeddedChunkWithRetry(ws, chunkId, i, fullChunk);
  }

  // 3. 發送完成訊息
  const chunkCompleteMessage = {
    type: 'image_chunk_complete',
    chunkId: chunkId,
    deviceMac: deviceMac,
    imageCode: imageCode,
    totalChecksum: calculateChecksum(rawBuffer),
    timestamp: new Date().toISOString()
  };
  ws.send(JSON.stringify(chunkCompleteMessage));

    // 4. 等待最終確認
    await waitForCompleteAck(ws, chunkId);

    console.log(`✅ 分片傳輸完成: ${totalChunks} 個分片, 總計 ${rawBuffer.length} bytes`);

    // 結束chunk傳輸狀態追蹤
    endChunkTransmission(gatewayId, chunkId);
  } catch (error) {
    // 發生錯誤時也要清理狀態
    endChunkTransmission(gatewayId, chunkId);
    throw error;
  }
};

// 直接傳輸（無需分片）
const sendDirectRawdata = async (ws, deviceMac, imageCode, rawBuffer, imageData = null, dataType = 'rawdata') => {
  const directMessage = {
    type: 'update_preview',
    deviceMac: deviceMac,
    imageCode: imageCode,
    rawdata: Array.from(rawBuffer),  // 轉換為數組格式
    dataType: dataType,              // 使用 dataType 來傳送格式信息
    timestamp: new Date().toISOString()
  };

  // 如果提供了 imageData，也一併發送（向後兼容）
  if (imageData) {
    directMessage.imageData = imageData;
    console.log(`✅ 直接傳輸包含 imageData: ${imageData.length} 字符`);
  }

  // 檢查 JSON 訊息大小
  const gatewayMac = ws.macAddress;
  if (gatewayMac) {
    const sizeCheck = checkJsonMessageSize(directMessage, gatewayMac);
    if (!sizeCheck.canSend) {
      console.log('📝 記錄因 update_preview JSON 訊息過大而被拒絕的任務');

      // 記錄被拒絕的任務到設備事件
      try {
        const { db } = await getDbConnection();
        const deviceCollection = db.collection('devices');
        const device = await deviceCollection.findOne({ macAddress: deviceMac });

        if (device) {
          await logDeviceEvent(device._id, 'image_update_rejected', {
            reason: 'update_preview_json_exceeds_max_single_message_size',
            messageSize: sizeCheck.messageSize,
            maxSingleMessageSize: sizeCheck.maxSingleMessageSize,
            gatewayMac: gatewayMac,
            imageCode: imageCode,
            timestamp: new Date().toISOString(),
            errorMessage: sizeCheck.error
          });
          console.log(`已記錄設備 ${device._id} 的 update_preview 訊息被拒絕事件`);
        }

        // 記錄網關事件
        const gatewayId = ws.gatewayId;
        if (gatewayId) {
          await logGatewayEvent(gatewayId, 'message_rejected', {
            reason: 'update_preview_json_exceeds_max_single_message_size',
            messageType: 'update_preview',
            deviceMac: deviceMac,
            messageSize: sizeCheck.messageSize,
            maxSingleMessageSize: sizeCheck.maxSingleMessageSize,
            imageCode: imageCode,
            timestamp: new Date().toISOString(),
            errorMessage: sizeCheck.error
          });
          console.log(`已記錄網關 ${gatewayId} 的 update_preview 訊息被拒絕事件`);
        }
      } catch (logError) {
        console.error('記錄被拒絕任務失敗:', logError);
      }

      throw new Error(sizeCheck.error);
    }
  }

  ws.send(JSON.stringify(directMessage));
  console.log(`✅ 直接傳輸完成: ${rawBuffer.length} bytes rawdata${imageData ? ' + imageData' : ''}`);
};

// ACK 等待機制實作
const waitForStartAck = async (ws, chunkId) => {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(`Start ACK timeout for chunk ${chunkId}`));
    }, 10000); // 10秒超時

    const ackHandler = (message) => {
      try {
        const data = JSON.parse(message);
        if (data.type === 'chunk_start_ack' && data.chunkId === chunkId) {
          clearTimeout(timeout);
          ws.off('message', ackHandler);

          if (data.status === 'ready') {
            resolve(data);
          } else {
            reject(new Error(`Start ACK error: ${data.error}`));
          }
        }
      } catch (e) {
        // 忽略非 JSON 訊息
      }
    };

    ws.on('message', ackHandler);
  });
};

const waitForCompleteAck = async (ws, chunkId) => {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(`Complete ACK timeout for chunk ${chunkId}`));
    }, 15000); // 15秒超時

    const ackHandler = (message) => {
      try {
        const data = JSON.parse(message);
        if (data.type === 'chunk_complete_ack' && data.chunkId === chunkId) {
          clearTimeout(timeout);
          ws.off('message', ackHandler);

          if (data.status === 'success') {
            resolve(data);
          } else {
            reject(new Error(`Complete ACK error: ${data.error}`));
          }
        }
      } catch (e) {
        // 忽略非 JSON 訊息
      }
    };

    ws.on('message', ackHandler);
  });
};

const waitForChunkAck = async (ws, chunkId, chunkIndex) => {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error(`Chunk ${chunkIndex} ACK timeout`));
    }, 5000); // 5秒超時

    const ackHandler = (message) => {
      try {
        const data = JSON.parse(message);
        if (data.type === 'chunk_ack' &&
            data.chunkId === chunkId &&
            data.chunkIndex === chunkIndex) {
          clearTimeout(timeout);
          ws.off('message', ackHandler);

          if (data.status === 'received' || data.status === 'duplicate') {
            // 'received' 和 'duplicate' 都視為成功
            resolve(data);
          } else {
            reject(new Error(`Chunk ${chunkIndex} error: ${data.error}`));
          }
        }
      } catch (e) {
        // 忽略非 JSON 訊息
      }
    };

    ws.on('message', ackHandler);
  });
};

// 嵌入式 Index 重傳機制
const sendEmbeddedChunkWithRetry = async (ws, chunkId, chunkIndex, fullChunk, maxRetries = 3) => {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      // 發送嵌入 index 的完整分片數據
      ws.send(fullChunk);

      // 等待 ACK
      await waitForChunkAck(ws, chunkId, chunkIndex);
      return; // 成功，退出重試循環

    } catch (error) {
      console.warn(`Chunk ${chunkIndex} attempt ${attempt + 1} failed:`, error);

      if (attempt === maxRetries - 1) {
        throw new Error(`Chunk ${chunkIndex} failed after ${maxRetries} attempts`);
      }

      // 等待後重試（重傳相同的嵌入 index 數據）
      await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
    }
  }
};

/**
 * 獲取 Gateway 偏好的 rawdata 格式
 * @param {string} gatewayMac - Gateway MAC 地址
 * @returns {string} 偏好的格式
 */
const getGatewayPreferredFormat = (gatewayMac) => {
  const preferredFormat = gatewayPreferredFormats.get(gatewayMac) || RAWDATA_FORMATS.RAWDATA;

  console.log(`Gateway ${gatewayMac} 偏好格式: ${preferredFormat}`);
  return preferredFormat;
};

/**
 * 處理 rawdata 格式轉換
 * @param {string} format - 目標格式
 * @param {Uint8Array} rawBuffer - 完整的 rawdata（包含 ImageInfo）
 * @returns {Object} 處理結果
 */
const processRawdataFormat = (format, rawBuffer) => {
  try {
    // 如果是 rawdata 格式，直接返回
    if (format === RAWDATA_FORMATS.RAWDATA) {
      console.log(`使用原始 rawdata 格式，無需轉換`);
      return {
        success: true,
        format: format,
        data: rawBuffer,
        compressionResult: {
          originalSize: rawBuffer.length,
          compressedSize: rawBuffer.length,
          compressionRatio: 1.0,
          processingTime: 0
        }
      };
    }

    // 分離 ImageInfo 和像素數據
    const IMAGE_INFO_SIZE = 12; // ImageInfo 固定 12 bytes

    if (rawBuffer.length < IMAGE_INFO_SIZE) {
      throw new Error('Rawdata too small to contain ImageInfo');
    }

    const imageInfoBytes = rawBuffer.slice(0, IMAGE_INFO_SIZE);
    const pixelData = rawBuffer.slice(IMAGE_INFO_SIZE);

    console.log(`處理 rawdata 格式轉換: 格式=${format}, ImageInfo=${IMAGE_INFO_SIZE} bytes, 像素數據=${pixelData.length} bytes`);

    // 處理像素數據
    const processingResult = rawdataCompression.processPixelData(format, pixelData);

    if (!processingResult.success) {
      throw new Error(`格式轉換失敗: ${processingResult.error}`);
    }

    // 重新組合 ImageInfo + 處理後的像素數據
    const finalBuffer = new Uint8Array(IMAGE_INFO_SIZE + processingResult.data.length);
    finalBuffer.set(imageInfoBytes, 0);
    finalBuffer.set(processingResult.data, IMAGE_INFO_SIZE);

    console.log(`格式轉換完成:`, {
      originalPixelSize: pixelData.length,
      processedPixelSize: processingResult.data.length,
      compressionRatio: `${(processingResult.processingRatio * 100).toFixed(1)}%`,
      finalSize: finalBuffer.length,
      processingTime: `${processingResult.processingTime.toFixed(2)}ms`
    });

    return {
      success: true,
      format: format,
      data: finalBuffer,
      compressionResult: {
        originalSize: rawBuffer.length,
        compressedSize: finalBuffer.length,
        compressionRatio: finalBuffer.length / rawBuffer.length,
        processingTime: processingResult.processingTime
      }
    };

  } catch (error) {
    console.error('Rawdata 格式轉換失敗:', error.message);
    return {
      success: false,
      format: RAWDATA_FORMATS.RAWDATA,
      data: rawBuffer, // 返回原始數據
      error: error.message
    };
  }
};

// 主要的圖片傳輸函數，根據 Gateway 能力動態決定傳輸方式
const sendImageToGateway = async (ws, deviceMac, imageCode, rawBuffer, imageData = null) => {
  console.log(`準備發送圖片到設備 ${deviceMac}: ${rawBuffer.length} bytes rawdata${imageData ? ' + imageData' : ''}`);

  try {
    // 從 WebSocket 連接中獲取網關的 MAC 地址
    const gatewayMac = ws.macAddress;
    const gatewayId = ws.gatewayId;
    if (!gatewayMac) {
      throw new Error('無法獲取網關MAC地址，WebSocket連接缺少macAddress屬性');
    }

    console.log(`目標設備: ${deviceMac}, 通過網關: ${gatewayMac}`);

    // 新增：獲取 Gateway 偏好的 rawdata 格式
    const preferredFormat = getGatewayPreferredFormat(gatewayMac);

    // 處理 rawdata 格式轉換
    const formatResult = processRawdataFormat(preferredFormat, rawBuffer);
    const finalRawBuffer = formatResult.data;
    const selectedFormat = formatResult.format;

    console.log(`使用格式: ${selectedFormat}, 數據大小: ${finalRawBuffer.length} bytes`);

    // 根據 Gateway 上報的能力判斷傳輸方式（使用網關MAC地址）
    if (shouldUseChunking(finalRawBuffer.length, gatewayMac, finalRawBuffer, imageData, selectedFormat)) {
      // 使用分片傳輸（分片傳輸只發送 rawdata，不發送 imageData）
      const chunkSize = getChunkSize(gatewayMac);
      console.log(`使用分片傳輸，分片大小: ${chunkSize} bytes`);

      await sendChunkedRawdataWithEmbeddedIndex(
        ws, deviceMac, imageCode, finalRawBuffer, chunkSize, selectedFormat
      );
    } else {
      // 直接傳輸（包含 imageData 以確保向後兼容）
      console.log('使用直接傳輸');
      await sendDirectRawdata(ws, deviceMac, imageCode, finalRawBuffer, imageData, selectedFormat);
    }

    console.log('✅ 圖片傳輸完成');

  } catch (error) {
    console.error('❌ 圖片傳輸失敗:', error);
    throw error;
  }
};

// 等待任一網關變為可用
const waitForAnyGatewayAvailable = (gatewayIds, maxWaitTime = 30000) => {
  return new Promise((resolve, reject) => {
    // 首先檢查是否已經有可用的網關
    const availableGateways = getAvailableGateways(gatewayIds);
    if (availableGateways.length > 0) {
      console.log(`🔍 已有可用網關: ${availableGateways.join(', ')}`);
      resolve(availableGateways[0]);
      return;
    }

    console.log(`⏳ 等待任一網關變為可用，網關列表: [${gatewayIds.join(', ')}]，最大等待時間: ${maxWaitTime}ms`);

    const timeout = setTimeout(() => {
      gatewayStatusEmitter.removeListener('gatewayAvailable', onGatewayAvailable);
      reject(new Error(`等待網關可用超時 (${maxWaitTime}ms)`));
    }, maxWaitTime);

    const onGatewayAvailable = (availableGatewayId) => {
      // 檢查這個網關是否在我們關心的列表中
      if (gatewayIds.includes(availableGatewayId)) {
        console.log(`🔔 網關 ${availableGatewayId} 變為可用`);
        clearTimeout(timeout);
        gatewayStatusEmitter.removeListener('gatewayAvailable', onGatewayAvailable);
        resolve(availableGatewayId);
      }
    };

    gatewayStatusEmitter.on('gatewayAvailable', onGatewayAvailable);
  });
};

// 批量傳送進度追蹤
const batchProgressSubscribers = new Map(); // batchId -> Set of client websockets

// 設備狀態即時更新追蹤
const deviceStatusSubscribers = new Map(); // storeId -> Set of client websockets
const globalDeviceStatusSubscribers = new Set(); // 全局設備狀態訂閱者

// 網關狀態即時更新追蹤
const gatewayStatusSubscribers = new Map(); // storeId -> Set of client websockets
const globalGatewayStatusSubscribers = new Set(); // 全局網關狀態訂閱者

// 門店資料更新追蹤
const storeDataUpdateSubscribers = new Map(); // storeId -> Set of client websockets
const globalStoreDataUpdateSubscribers = new Set(); // 全局門店資料訂閱者

// 系統資料更新追蹤
const systemDataUpdateSubscribers = new Set(); // 系統資料訂閱者

// 模板更新追蹤
const templateUpdateSubscribers = new Map(); // storeId -> Set of client websockets
const globalTemplateUpdateSubscribers = new Set(); // 全局模板訂閱者

// 刷圖計畫更新追蹤
const refreshPlanUpdateSubscribers = new Map(); // storeId -> Set of client websockets

// 廣播批量傳送進度
const broadcastBatchProgress = (batchId, progressData) => {
  const subscribers = batchProgressSubscribers.get(batchId);
  if (!subscribers || subscribers.size === 0) {
    return;
  }

  const message = JSON.stringify({
    type: 'batch_progress',
    batchId,
    ...progressData,
    timestamp: new Date().toISOString()
  });

  console.log(`📡 廣播批量傳送進度 ${batchId}:`, progressData);

  subscribers.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(message);
      } catch (error) {
        console.error('發送批量進度消息失敗:', error);
        subscribers.delete(ws);
      }
    } else {
      subscribers.delete(ws);
    }
  });

  // 如果沒有訂閱者了，清理
  if (subscribers.size === 0) {
    batchProgressSubscribers.delete(batchId);
  }
};

// 廣播批量傳送完成
const broadcastBatchComplete = (batchId, result) => {
  const subscribers = batchProgressSubscribers.get(batchId);
  if (!subscribers || subscribers.size === 0) {
    return;
  }

  const message = JSON.stringify({
    type: 'batch_complete',
    batchId,
    result,
    timestamp: new Date().toISOString()
  });

  console.log(`🎯 廣播批量傳送完成 ${batchId}:`, result);

  subscribers.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(message);
      } catch (error) {
        console.error('發送批量完成消息失敗:', error);
      }
    }
  });

  // 清理訂閱者
  batchProgressSubscribers.delete(batchId);
};

// 訂閱批量傳送進度
const subscribeBatchProgress = (batchId, clientWs) => {
  if (!batchProgressSubscribers.has(batchId)) {
    batchProgressSubscribers.set(batchId, new Set());
  }
  batchProgressSubscribers.get(batchId).add(clientWs);
  console.log(`📝 客戶端訂閱批量傳送進度: ${batchId}`);
};

// 取消訂閱批量傳送進度
const unsubscribeBatchProgress = (batchId, clientWs) => {
  const subscribers = batchProgressSubscribers.get(batchId);
  if (subscribers) {
    subscribers.delete(clientWs);
    if (subscribers.size === 0) {
      batchProgressSubscribers.delete(batchId);
    }
  }
  console.log(`📝 客戶端取消訂閱批量傳送進度: ${batchId}`);
};

// 獲取批量進度訂閱者（調試用）
const getBatchProgressSubscribers = () => {
  return batchProgressSubscribers;
};

// ==================== 設備狀態即時更新功能 ====================

// 訂閱設備狀態
const subscribeDeviceStatus = (ws, storeId = null, options = {}) => {
  console.log(`前端客戶端訂閱設備狀態: storeId=${storeId}`);

  if (storeId) {
    if (!deviceStatusSubscribers.has(storeId)) {
      deviceStatusSubscribers.set(storeId, new Set());
    }
    deviceStatusSubscribers.get(storeId).add(ws);
  } else {
    globalDeviceStatusSubscribers.add(ws);
  }

  // 保存訂閱選項到WebSocket實例
  ws.deviceStatusOptions = options;
  ws.subscribedStoreId = storeId;

  // 發送訂閱確認
  ws.send(JSON.stringify({
    type: 'device_status_subscription_ack',
    storeId,
    subscribed: true,
    timestamp: new Date().toISOString()
  }));
};

// 取消訂閱設備狀態
const unsubscribeDeviceStatus = (ws, storeId = null) => {
  if (storeId && deviceStatusSubscribers.has(storeId)) {
    deviceStatusSubscribers.get(storeId).delete(ws);
    // 如果該門店沒有訂閱者了，清理Map
    if (deviceStatusSubscribers.get(storeId).size === 0) {
      deviceStatusSubscribers.delete(storeId);
    }
  } else {
    globalDeviceStatusSubscribers.delete(ws);
  }

  delete ws.deviceStatusOptions;
  delete ws.subscribedStoreId;
};

// 獲取指定門店的設備狀態訂閱者
const getDeviceStatusSubscribers = (storeId) => {
  const storeSubscribers = deviceStatusSubscribers.get(storeId) || new Set();
  return new Set([...storeSubscribers, ...globalDeviceStatusSubscribers]);
};

// ==================== 網關狀態即時更新功能 ====================

// 訂閱網關狀態
const subscribeGatewayStatus = (ws, storeId = null, options = {}) => {
  console.log(`前端客戶端訂閱網關狀態: storeId=${storeId}`);

  if (storeId) {
    if (!gatewayStatusSubscribers.has(storeId)) {
      gatewayStatusSubscribers.set(storeId, new Set());
    }
    gatewayStatusSubscribers.get(storeId).add(ws);
  } else {
    globalGatewayStatusSubscribers.add(ws);
  }

  // 保存訂閱選項到WebSocket實例
  ws.gatewayStatusOptions = options;
  ws.subscribedGatewayStoreId = storeId;

  // 發送訂閱確認
  ws.send(JSON.stringify({
    type: 'gateway_status_subscription_ack',
    storeId,
    subscribed: true,
    timestamp: new Date().toISOString()
  }));
};

// 取消訂閱網關狀態
const unsubscribeGatewayStatus = (ws, storeId = null) => {
  if (storeId && gatewayStatusSubscribers.has(storeId)) {
    gatewayStatusSubscribers.get(storeId).delete(ws);
    // 如果該門店沒有訂閱者了，清理Map
    if (gatewayStatusSubscribers.get(storeId).size === 0) {
      gatewayStatusSubscribers.delete(storeId);
    }
  } else {
    globalGatewayStatusSubscribers.delete(ws);
  }

  delete ws.gatewayStatusOptions;
  delete ws.subscribedGatewayStoreId;
};

// 獲取指定門店的網關狀態訂閱者
const getGatewayStatusSubscribers = (storeId) => {
  const storeSubscribers = gatewayStatusSubscribers.get(storeId) || new Set();
  return new Set([...storeSubscribers, ...globalGatewayStatusSubscribers]);
};

// ==================== 設備狀態廣播器 ====================

// 設備狀態廣播器
const deviceStatusBroadcaster = {
  pendingUpdates: new Map(), // storeId -> Array<deviceUpdates>
  broadcastTimer: null,
  DEBOUNCE_DELAY: 500, // 500ms防抖延遲

  // 安排設備狀態更新廣播
  scheduleUpdate(storeId, deviceUpdates) {
    if (!this.pendingUpdates.has(storeId)) {
      this.pendingUpdates.set(storeId, []);
    }

    // 合併設備更新（避免重複）
    const existingUpdates = this.pendingUpdates.get(storeId);
    const mergedUpdates = this.mergeDeviceUpdates(existingUpdates, deviceUpdates);
    this.pendingUpdates.set(storeId, mergedUpdates);

    // 設置防抖定時器
    if (this.broadcastTimer) {
      clearTimeout(this.broadcastTimer);
    }

    this.broadcastTimer = setTimeout(() => {
      this.flushPendingUpdates();
    }, this.DEBOUNCE_DELAY);
  },

  // 合併設備更新，避免重複
  mergeDeviceUpdates(existing, newUpdates) {
    const deviceMap = new Map();

    // 先添加現有更新
    existing.forEach(device => {
      deviceMap.set(device._id, device);
    });

    // 覆蓋或添加新更新
    newUpdates.forEach(device => {
      deviceMap.set(device._id, device);
    });

    return Array.from(deviceMap.values());
  },

  // 刷新待處理的更新
  flushPendingUpdates() {
    for (const [storeId, updates] of this.pendingUpdates) {
      if (updates.length > 0) {
        this.broadcastToStore(storeId, updates);
      }
    }
    this.pendingUpdates.clear();
    this.broadcastTimer = null;
  },

  // 向指定門店廣播設備狀態
  broadcastToStore(storeId, deviceUpdates) {
    const subscribers = getDeviceStatusSubscribers(storeId);

    if (subscribers.size === 0) {
      console.log(`門店 ${storeId} 沒有設備狀態訂閱者`);
      return;
    }

    const event = {
      type: 'device_status_update',
      storeId,
      devices: deviceUpdates,
      timestamp: new Date().toISOString(),
      updateType: deviceUpdates.length > 10 ? 'batch' : 'single'
    };

    console.log(`廣播設備狀態到門店 ${storeId}: ${deviceUpdates.length} 個設備, ${subscribers.size} 個訂閱者`);

    subscribers.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify(event));
        } catch (error) {
          console.error('廣播設備狀態失敗:', error);
        }
      }
    });
  }
};

// ==================== 網關狀態廣播器 ====================

// 網關狀態廣播器
const gatewayStatusBroadcaster = {
  pendingUpdates: new Map(), // storeId -> Array<gatewayUpdates>
  broadcastTimer: null,
  DEBOUNCE_DELAY: 300, // 網關狀態變更較少，使用較短的防抖延遲

  // 安排網關狀態更新廣播
  scheduleUpdate(storeId, gatewayUpdates) {
    if (!this.pendingUpdates.has(storeId)) {
      this.pendingUpdates.set(storeId, []);
    }

    // 合併網關更新（避免重複）
    const existingUpdates = this.pendingUpdates.get(storeId);
    const mergedUpdates = this.mergeGatewayUpdates(existingUpdates, gatewayUpdates);
    this.pendingUpdates.set(storeId, mergedUpdates);

    // 設置防抖定時器
    if (this.broadcastTimer) {
      clearTimeout(this.broadcastTimer);
    }

    this.broadcastTimer = setTimeout(() => {
      this.flushPendingUpdates();
    }, this.DEBOUNCE_DELAY);
  },

  // 合併網關更新，避免重複
  mergeGatewayUpdates(existing, newUpdates) {
    const gatewayMap = new Map();

    // 先添加現有更新
    existing.forEach(gateway => {
      gatewayMap.set(gateway._id, gateway);
    });

    // 覆蓋或添加新更新
    newUpdates.forEach(gateway => {
      gatewayMap.set(gateway._id, gateway);
    });

    return Array.from(gatewayMap.values());
  },

  // 刷新待處理的更新
  flushPendingUpdates() {
    for (const [storeId, updates] of this.pendingUpdates) {
      if (updates.length > 0) {
        this.broadcastToStore(storeId, updates);
      }
    }
    this.pendingUpdates.clear();
    this.broadcastTimer = null;
  },

  // 向指定門店廣播網關狀態
  broadcastToStore(storeId, gatewayUpdates) {
    const subscribers = getGatewayStatusSubscribers(storeId);

    if (subscribers.size === 0) {
      console.log(`門店 ${storeId} 沒有網關狀態訂閱者`);
      return;
    }

    const event = {
      type: 'gateway_status_update',
      storeId,
      gateways: gatewayUpdates,
      timestamp: new Date().toISOString(),
      updateType: gatewayUpdates.some(g => g.updatedFields.includes('status')) ? 'status' : 'info'
    };

    console.log(`廣播網關狀態到門店 ${storeId}: ${gatewayUpdates.length} 個網關, ${subscribers.size} 個訂閱者`);

    subscribers.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify(event));
        } catch (error) {
          console.error('廣播網關狀態失敗:', error);
        }
      }
    });
  }
};

// ==================== 門店資料更新功能 ====================

// 訂閱門店資料更新
const subscribeStoreDataUpdate = (ws, storeId, options = {}) => {
  console.log(`前端客戶端訂閱門店資料更新: storeId=${storeId}`);

  if (storeId) {
    if (!storeDataUpdateSubscribers.has(storeId)) {
      storeDataUpdateSubscribers.set(storeId, new Set());
    }
    storeDataUpdateSubscribers.get(storeId).add(ws);
  } else {
    globalStoreDataUpdateSubscribers.add(ws);
  }

  // 保存訂閱選項到WebSocket實例
  ws.storeDataUpdateOptions = options;
  ws.subscribedStoreDataStoreId = storeId;

  // 發送訂閱確認
  ws.send(JSON.stringify({
    type: 'store_data_subscription_ack',
    storeId,
    subscribed: true,
    timestamp: new Date().toISOString()
  }));
};

// 取消訂閱門店資料更新
const unsubscribeStoreDataUpdate = (ws, storeId) => {
  if (storeId && storeDataUpdateSubscribers.has(storeId)) {
    storeDataUpdateSubscribers.get(storeId).delete(ws);
    // 如果該門店沒有訂閱者了，清理Map
    if (storeDataUpdateSubscribers.get(storeId).size === 0) {
      storeDataUpdateSubscribers.delete(storeId);
    }
  } else {
    globalStoreDataUpdateSubscribers.delete(ws);
  }

  delete ws.storeDataUpdateOptions;
  delete ws.subscribedStoreDataStoreId;
};

// 獲取指定門店的門店資料訂閱者
const getStoreDataUpdateSubscribers = (storeId) => {
  const storeSubscribers = storeDataUpdateSubscribers.get(storeId) || new Set();
  return new Set([...storeSubscribers, ...globalStoreDataUpdateSubscribers]);
};

// 廣播門店資料更新
const broadcastStoreDataUpdate = (storeId, storeDataUpdates, updateType = 'update') => {
  const subscribers = getStoreDataUpdateSubscribers(storeId);

  if (subscribers.size === 0) {
    console.log(`門店 ${storeId} 沒有門店資料訂閱者`);
    return;
  }

  const event = {
    type: 'store_data_update',
    storeId,
    storeData: storeDataUpdates,
    timestamp: new Date().toISOString(),
    updateType
  };

  console.log(`廣播門店資料更新到門店 ${storeId}: ${storeDataUpdates.length} 筆資料, ${subscribers.size} 個訂閱者`);

  subscribers.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(event));
      } catch (error) {
        console.error('廣播門店資料更新失敗:', error);
      }
    }
  });
};

// ==================== 系統資料更新功能 ====================

// 訂閱系統資料更新
const subscribeSystemDataUpdate = (ws, options = {}) => {
  console.log('前端客戶端訂閱系統資料更新');

  systemDataUpdateSubscribers.add(ws);

  // 保存訂閱選項到WebSocket實例
  ws.systemDataUpdateOptions = options;

  // 發送訂閱確認
  ws.send(JSON.stringify({
    type: 'system_data_subscription_ack',
    subscribed: true,
    timestamp: new Date().toISOString()
  }));
};

// 取消訂閱系統資料更新
const unsubscribeSystemDataUpdate = (ws) => {
  systemDataUpdateSubscribers.delete(ws);

  delete ws.systemDataUpdateOptions;
};

// 廣播系統資料更新
const broadcastSystemDataUpdate = (systemDataUpdates, updateType = 'update') => {
  if (systemDataUpdateSubscribers.size === 0) {
    console.log('沒有系統資料訂閱者');
    return;
  }

  const event = {
    type: 'system_data_update',
    systemData: systemDataUpdates,
    timestamp: new Date().toISOString(),
    updateType
  };

  console.log(`廣播系統資料更新: ${systemDataUpdates.length} 筆資料, ${systemDataUpdateSubscribers.size} 個訂閱者`);

  systemDataUpdateSubscribers.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(event));
      } catch (error) {
        console.error('廣播系統資料更新失敗:', error);
      }
    }
  });
};

// ==================== 模板更新功能 ====================

// 訂閱模板更新
const subscribeTemplateUpdate = (ws, storeId, options = {}) => {
  console.log(`前端客戶端訂閱模板更新: storeId=${storeId || 'all'}`);

  if (storeId) {
    if (!templateUpdateSubscribers.has(storeId)) {
      templateUpdateSubscribers.set(storeId, new Set());
    }
    templateUpdateSubscribers.get(storeId).add(ws);
  } else {
    globalTemplateUpdateSubscribers.add(ws);
  }

  // 保存訂閱選項到WebSocket實例
  ws.templateUpdateOptions = options;
  ws.subscribedTemplateStoreId = storeId;

  // 發送訂閱確認
  ws.send(JSON.stringify({
    type: 'template_subscription_ack',
    storeId,
    subscribed: true,
    timestamp: new Date().toISOString()
  }));
};

// 取消訂閱模板更新
const unsubscribeTemplateUpdate = (ws, storeId) => {
  if (storeId && templateUpdateSubscribers.has(storeId)) {
    templateUpdateSubscribers.get(storeId).delete(ws);
    // 如果該門店沒有訂閱者了，清理Map
    if (templateUpdateSubscribers.get(storeId).size === 0) {
      templateUpdateSubscribers.delete(storeId);
    }
  } else {
    globalTemplateUpdateSubscribers.delete(ws);
  }

  delete ws.templateUpdateOptions;
  delete ws.subscribedTemplateStoreId;
};

// 獲取指定門店的模板訂閱者
const getTemplateUpdateSubscribers = (storeId) => {
  const storeSubscribers = templateUpdateSubscribers.get(storeId) || new Set();
  return new Set([...storeSubscribers, ...globalTemplateUpdateSubscribers]);
};

// 廣播模板更新
const broadcastTemplateUpdate = (storeId, templateUpdates, updateType = 'update') => {
  const subscribers = getTemplateUpdateSubscribers(storeId);

  if (subscribers.size === 0) {
    console.log(`門店 ${storeId || 'all'} 沒有模板訂閱者`);
    return;
  }

  const event = {
    type: 'template_update',
    storeId,
    templates: templateUpdates,
    timestamp: new Date().toISOString(),
    updateType
  };

  console.log(`廣播模板更新到門店 ${storeId || 'all'}: ${templateUpdates.length} 個模板, ${subscribers.size} 個訂閱者`);

  subscribers.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(event));
      } catch (error) {
        console.error('廣播模板更新失敗:', error);
      }
    }
  });

  // 如果是門店特定的模板更新，也要廣播給全局訂閱者
  if (storeId && globalTemplateUpdateSubscribers.size > 0) {
    globalTemplateUpdateSubscribers.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify(event));
        } catch (error) {
          console.error('廣播模板更新到全局訂閱者失敗:', error);
        }
      }
    });
  }
};

// ==================== 刷圖計畫更新功能 ====================

// 訂閱刷圖計畫更新
const subscribeRefreshPlanUpdate = (ws, storeId, options = {}) => {
  console.log(`前端客戶端訂閱刷圖計畫更新: storeId=${storeId}`);

  if (!refreshPlanUpdateSubscribers.has(storeId)) {
    refreshPlanUpdateSubscribers.set(storeId, new Set());
  }
  refreshPlanUpdateSubscribers.get(storeId).add(ws);

  // 保存訂閱選項到WebSocket實例
  ws.refreshPlanUpdateOptions = options;
  ws.subscribedRefreshPlanStoreId = storeId;

  // 發送訂閱確認
  ws.send(JSON.stringify({
    type: 'refresh_plan_subscription_ack',
    storeId,
    subscribed: true,
    timestamp: new Date().toISOString()
  }));
};

// 取消訂閱刷圖計畫更新
const unsubscribeRefreshPlanUpdate = (ws, storeId) => {
  if (storeId && refreshPlanUpdateSubscribers.has(storeId)) {
    refreshPlanUpdateSubscribers.get(storeId).delete(ws);
    // 如果該門店沒有訂閱者了，清理Map
    if (refreshPlanUpdateSubscribers.get(storeId).size === 0) {
      refreshPlanUpdateSubscribers.delete(storeId);
    }
  }

  delete ws.refreshPlanUpdateOptions;
  delete ws.subscribedRefreshPlanStoreId;
};

// 獲取指定門店的刷圖計畫訂閱者
const getRefreshPlanUpdateSubscribers = (storeId) => {
  return refreshPlanUpdateSubscribers.get(storeId) || new Set();
};

// 廣播刷圖計畫更新
const broadcastRefreshPlanUpdate = (storeId, planUpdate, updateType = 'update') => {
  const subscribers = getRefreshPlanUpdateSubscribers(storeId);

  if (subscribers.size === 0) {
    console.log(`門店 ${storeId} 沒有刷圖計畫訂閱者`);
    return;
  }

  const event = {
    type: 'refresh_plan_update',
    storeId,
    planId: planUpdate.planId,
    planData: planUpdate.planData,
    timestamp: new Date().toISOString(),
    updateType
  };

  console.log(`廣播刷圖計畫更新到門店 ${storeId}: 計畫 ${planUpdate.planId}, ${subscribers.size} 個訂閱者, 操作類型: ${updateType}`);

  subscribers.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(event));
      } catch (error) {
        console.error('廣播刷圖計畫更新失敗:', error);
      }
    }
  });
};

// 公開的 API
module.exports = {
  initDB,
  initWebSocketServer,
  resetAllGatewayStatus,
  updateGatewayStatus,
  updateDeviceStatus,
  setupHeartbeatCheck,
  getConnectedGateways: () => connectedGateways,
  getGatewayConnectionStats,
  isGatewayOnline,
  sendCommandToGateway,
  logGatewayEvent,
  logDeviceEvent,
  // 新增分片傳輸相關API
  shouldUseChunking,
  getChunkSize,
  sendImageToGateway,
  // 新增 JSON 訊息大小檢查API
  checkJsonMessageSize,
  safeSendJsonToGateway,
  // 新增智能網關選擇相關API
  isGatewayBusyWithChunk,
  getAvailableGateways,
  getGatewayTransmissionStatus,
  // 新增chunk傳輸狀態追蹤API
  startChunkTransmission,
  endChunkTransmission,
  // 新增等待網關可用API
  waitForAnyGatewayAvailable,
  // 新增批量傳送進度API
  broadcastBatchProgress,
  broadcastBatchComplete,
  subscribeBatchProgress,
  unsubscribeBatchProgress,
  getBatchProgressSubscribers,
  // 新增設備狀態即時更新API
  broadcastDeviceStatus: (storeId, deviceUpdates) => {
    deviceStatusBroadcaster.scheduleUpdate(storeId, deviceUpdates);
  },
  subscribeDeviceStatus,
  unsubscribeDeviceStatus,
  getDeviceStatusSubscribers,
  // 新增網關狀態即時更新API
  broadcastGatewayStatus: (storeId, gatewayUpdates) => {
    gatewayStatusBroadcaster.scheduleUpdate(storeId, gatewayUpdates);
  },
  subscribeGatewayStatus,
  unsubscribeGatewayStatus,
  getGatewayStatusSubscribers,
  // 新增門店資料更新API
  broadcastStoreDataUpdate,
  subscribeStoreDataUpdate,
  unsubscribeStoreDataUpdate,
  getStoreDataUpdateSubscribers,
  // 新增系統資料更新API
  broadcastSystemDataUpdate,
  subscribeSystemDataUpdate,
  unsubscribeSystemDataUpdate,
  // 新增模板更新API
  broadcastTemplateUpdate,
  subscribeTemplateUpdate,
  unsubscribeTemplateUpdate,
  getTemplateUpdateSubscribers,
  // 新增刷圖計畫更新API
  broadcastRefreshPlanUpdate,
  subscribeRefreshPlanUpdate,
  unsubscribeRefreshPlanUpdate,
  getRefreshPlanUpdateSubscribers,
  // 新增設備和網關CRUD廣播API
  broadcastDeviceCRUD: (storeId, deviceUpdates, updateType = 'update') => {
    // 使用設備狀態廣播器，但推送device_crud_update事件
    const subscribers = getDeviceStatusSubscribers(storeId);

    if (subscribers.size === 0) {
      console.log(`門店 ${storeId} 沒有設備訂閱者`);
      return;
    }

    const event = {
      type: 'device_crud_update',
      storeId,
      devices: deviceUpdates,
      timestamp: new Date().toISOString(),
      updateType
    };

    console.log(`廣播設備CRUD事件到門店 ${storeId}: ${deviceUpdates.length} 個設備, ${subscribers.size} 個訂閱者, 操作類型: ${updateType}`);

    subscribers.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify(event));
        } catch (error) {
          console.error('廣播設備CRUD事件失敗:', error);
        }
      }
    });
  },
  broadcastGatewayCRUD: (storeId, gatewayUpdates, updateType = 'update') => {
    // 使用網關狀態廣播器，但推送gateway_crud_update事件
    const subscribers = getGatewayStatusSubscribers(storeId);

    if (subscribers.size === 0) {
      console.log(`門店 ${storeId} 沒有網關訂閱者`);
      return;
    }

    const event = {
      type: 'gateway_crud_update',
      storeId,
      gateways: gatewayUpdates,
      timestamp: new Date().toISOString(),
      updateType
    };

    console.log(`廣播網關CRUD事件到門店 ${storeId}: ${gatewayUpdates.length} 個網關, ${subscribers.size} 個訂閱者, 操作類型: ${updateType}`);

    subscribers.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        try {
          ws.send(JSON.stringify(event));
        } catch (error) {
          console.error('廣播網關CRUD事件失敗:', error);
        }
      }
    });
  }
};
