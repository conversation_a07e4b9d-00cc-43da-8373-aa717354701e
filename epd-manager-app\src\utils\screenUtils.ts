// EPD Manager App - 屏幕尺寸工具函數

// 屏幕配置接口
export interface ScreenConfig {
  id: string;
  name: string;
  displayName: string;
  width: number;
  height: number;
  supportedColors: string[];
}

// 屏幕配置數據（與服務器端保持一致）
export const screenConfigs: ScreenConfig[] = [
  {
    id: "2_13inch",
    name: "2.13\"",
    displayName: "2.13\" (122x250)",
    width: 122,
    height: 250,
    supportedColors: ["BWR", "Black & White & Red"]
  },
  {
    id: "2_9inch", 
    name: "2.9\"",
    displayName: "2.9\" (128x296)",
    width: 128,
    height: 296,
    supportedColors: ["BWR", "Black & White & Red"]
  },
  {
    id: "3_7inch",
    name: "3.7\"",
    displayName: "3.7\" (240x416)",
    width: 240,
    height: 416,
    supportedColors: ["BWRY", "Black & White & Red & Yellow"]
  },
  {
    id: "6inch",
    name: "6\"",
    displayName: "6\" (1024x758)",
    width: 1024,
    height: 758,
    supportedColors: ["BW", "Gray16"]
  },
  {
    id: "7_5inch",
    name: "7.5\"",
    displayName: "7.5\" (800x480)",
    width: 800,
    height: 480,
    supportedColors: ["BWR", "Black & White & Red"]
  }
];

/**
 * 解析尺寸字符串為寬高對象
 * 支持多種格式：
 * - "2.9\"" -> 查找配置獲取 {width: 128, height: 296}
 * - "128x296" -> 直接解析 {width: 128, height: 296}
 * - "296x128" -> 直接解析 {width: 296, height: 128}
 */
export const parseSizeString = (sizeStr: string): { width: number; height: number } | null => {
  if (!sizeStr) return null;

  // 首先嘗試解析 "寬x高" 格式
  const directMatch = sizeStr.match(/(\d+)x(\d+)/);
  if (directMatch && directMatch.length === 3) {
    return {
      width: parseInt(directMatch[1], 10),
      height: parseInt(directMatch[2], 10)
    };
  }

  // 嘗試解析尺寸標識符（如 "2.9\"", "6\"" 等）
  const sizeIdentifierMatch = sizeStr.match(/(\d+(?:\.\d+)?)"?/);
  if (sizeIdentifierMatch) {
    const sizeIdentifier = sizeIdentifierMatch[1] + '"';
    
    // 從屏幕配置中查找匹配的尺寸
    const config = screenConfigs.find(c => c.name === sizeIdentifier);
    if (config) {
      return {
        width: config.width,
        height: config.height
      };
    }
  }

  return null;
};

/**
 * 檢查兩個尺寸是否匹配
 * 考慮寬高和高寬的旋轉情況
 */
export const isSizeMatch = (size1: string, size2: string): boolean => {
  const dimensions1 = parseSizeString(size1);
  const dimensions2 = parseSizeString(size2);

  if (!dimensions1 || !dimensions2) {
    return false;
  }

  // 檢查直接匹配
  if (dimensions1.width === dimensions2.width && dimensions1.height === dimensions2.height) {
    return true;
  }

  // 檢查旋轉匹配（寬高互換）
  if (dimensions1.width === dimensions2.height && dimensions1.height === dimensions2.width) {
    return true;
  }

  return false;
};

/**
 * 獲取尺寸的標準化字符串表示
 * 返回 "寬x高" 格式
 */
export const getStandardSizeString = (sizeStr: string): string | null => {
  const dimensions = parseSizeString(sizeStr);
  if (!dimensions) return null;
  
  return `${dimensions.width}x${dimensions.height}`;
};

/**
 * 獲取尺寸的顯示名稱
 * 如果是已知配置，返回 "2.9\" (128x296)" 格式
 * 否則返回 "128x296" 格式
 */
export const getSizeDisplayName = (sizeStr: string): string => {
  const dimensions = parseSizeString(sizeStr);
  if (!dimensions) return sizeStr;

  // 查找匹配的配置
  const config = screenConfigs.find(c => 
    (c.width === dimensions.width && c.height === dimensions.height) ||
    (c.width === dimensions.height && c.height === dimensions.width)
  );

  if (config) {
    return config.displayName;
  }

  return `${dimensions.width}x${dimensions.height}`;
};

/**
 * 獲取屏幕配置通過尺寸字符串
 */
export const getScreenConfigBySize = (sizeStr: string): ScreenConfig | null => {
  const dimensions = parseSizeString(sizeStr);
  if (!dimensions) return null;

  return screenConfigs.find(c => 
    (c.width === dimensions.width && c.height === dimensions.height) ||
    (c.width === dimensions.height && c.height === dimensions.width)
  ) || null;
};

/**
 * 檢查設備尺寸是否支持指定的顏色類型
 */
export const isSizeColorCompatible = (sizeStr: string, colorType: string): boolean => {
  const config = getScreenConfigBySize(sizeStr);
  if (!config) return false;

  return config.supportedColors.includes(colorType);
};

/**
 * 獲取尺寸支持的顏色類型列表
 */
export const getSupportedColors = (sizeStr: string): string[] => {
  const config = getScreenConfigBySize(sizeStr);
  return config ? config.supportedColors : [];
};
