// EPD Manager App - TypeScript 類型定義

// 用戶相關類型
export interface User {
  _id: string;
  username: string;
  email?: string;
  roles: string[];
  permissions: string[];
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 門店相關類型
export interface Store {
  _id: string;
  id: string;
  name: string;
  address: string;
  phone?: string;
  managerId?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// 網關相關類型
export interface Gateway {
  _id: string;
  name: string;
  macAddress: string;
  status: 'online' | 'offline';
  model: string;
  wifiFirmwareVersion: string;
  btFirmwareVersion: string;
  ipAddress: string;
  storeId: string;
  lastSeen: string;
  websocket?: WebSocketConfig;
  devices?: string[];
  createdAt: string;
  updatedAt: string;
}

// WebSocket 配置類型
export interface WebSocketConfig {
  url: string;
  path: string;
  token: string;
  protocol: string;
}

// 設備相關類型
export interface Device {
  _id?: string;
  macAddress: string;
  status: 'online' | 'offline';
  dataId?: string;
  storeId?: string;
  initialized?: boolean;
  primaryGatewayId?: string;
  otherGateways?: string[];
  userId?: string;
  lastSeen?: string;
  note?: string;
  data: DeviceData;
  createdAt?: string;
  updatedAt?: string;
}

// 設備數據類型
export interface DeviceData {
  size: string;
  rssi: number;
  battery: number;
  imageCode?: string;
  colorType: 'BW' | 'BWR' | 'BWRY';
}

// 設備配置類型（用於添加設備）
export interface DeviceConfig {
  macAddress: string;
  status?: 'online' | 'offline';
  size?: string;
  colorType?: 'BW' | 'BWR' | 'BWRY';
  imageCode?: string;
}

// 藍芽設備類型（用於配對流程）
export interface BluetoothDevice {
  id: string;
  name: string;
  macAddress: string;
  selected: boolean;
  rssi?: number;
  deviceType?: 'gateway' | 'display';
}

// 網關配置類型（用於創建網關）
export interface GatewayConfig {
  name: string;
  macAddress: string;
  status: 'online' | 'offline';
  model: string;
  wifiFirmwareVersion: string;
  btFirmwareVersion: string;
  ipAddress: string;
  storeId: string;
  lastSeen: Date;
}

// API 回應類型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 自動配對結果類型
export interface AutoPairingResult {
  success: boolean;
  gateway?: Gateway;
  error?: string;
}

// WebSocket 消息類型
export interface WebSocketMessage {
  type: string;
  timestamp: number;
  [key: string]: any;
}

// 連接狀態類型
export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';

// 認證狀態類型
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

// 網關狀態類型
export interface GatewayState {
  gateways: Gateway[];
  selectedGateway: Gateway | null;
  connectionStatus: ConnectionStatus;
  loading: boolean;
  error: string | null;
}

// 設備狀態類型
export interface DeviceState {
  devices: Device[];
  customDevices: Device[];
  loading: boolean;
  error: string | null;
}

// 門店狀態類型
export interface StoreState {
  stores: Store[];
  selectedStore: Store | null;
  loading: boolean;
  error: string | null;
}

// WebSocket 狀態類型
export interface WebSocketState {
  isConnected: boolean;
  connectionStatus: ConnectionStatus;
  messages: WebSocketMessage[];
  error: string | null;
}

// 應用設置類型
export interface AppSettings {
  serverUrl: string;
  autoReconnect: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  rememberLogin: boolean;
}

// 日誌級別類型
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// 日誌條目類型
export interface LogEntry {
  id: string;
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
}

// 模板相關類型
export enum TemplateType {
  SINGLE_DATA = "Single data template",
  MULTIPLE_DATA = "Multiple data template"
}

// 數據綁定信息接口
export interface DataBinding {
  dataIndex: number;
  fieldId: string;
  displayName?: string;
  displayOptions?: {
    showPrefix?: boolean;
  };
  selectedStoreId?: string;
}

export interface TemplateElement {
  id: string;
  type: 'text' | 'image' | 'qrcode' | 'barcode' | 'icon' | 'multiline-text';
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  content?: string;
  src?: string;
  fontSize?: number;
  fontWeight?: string;
  color?: string;
  backgroundColor?: string;
  textAlign?: 'left' | 'center' | 'right';
  borderWidth?: number;
  borderColor?: string;
  borderRadius?: number;
  opacity?: number;
  zIndex?: number;
  // 數據綁定相關屬性
  dataBinding?: DataBinding;
  dataFieldId?: string; // 舊版兼容
  [key: string]: any;
}

export interface Template {
  id: string;
  name: string;
  type: TemplateType;
  screenSize: string;
  color: string;
  orientation: string;
  elements: TemplateElement[];
  previewImage?: string; // base64 格式的預覽圖
  storeId?: string;      // 關聯的門店ID，如果為空則表示可用於所有門店
  isSystemTemplate?: boolean; // 是否為系統模板
  createdAt?: string;
  updatedAt?: string;
}

// 模板狀態類型
export interface TemplateState {
  templates: Template[];
  loading: boolean;
  error: string | null;
}

// 模板篩選類型
export interface TemplateFilter {
  type?: 'all' | 'system' | 'store';
  screenSize?: string;
  colorType?: string;
  searchTerm?: string;
}

// 數據字段類型
export interface DataField {
  id: string;
  name: string;
  type: string;
  description?: string;
}

// 設備綁定數據類型
export interface DeviceBindingData {
  templateId: string;
  dataBindings: Record<string, string>;
  sendToGateway?: boolean;
}
