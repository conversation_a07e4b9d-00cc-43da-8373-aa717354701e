# EPD Manager App - 設備數據保護測試

## 修復說明

修復了WebSocket更新時設備固定屬性（size、colorType）被覆蓋的問題。

## 修復原理

### 問題根源
```typescript
// 修復前的問題邏輯
updatedDevices[index] = {
  ...existingDevice,    // 現有數據: { data: { size: "2.9\"", colorType: "BW", battery: 85 } }
  ...updatedDevice,     // WebSocket數據: { data: { battery: 90, rssi: -45 } }
};
// 結果: { data: { battery: 90, rssi: -45 } } - size和colorType丟失！
```

### 修復邏輯
```typescript
// 修復後的智能合併邏輯
data: {
  // 固定屬性保護：優先使用現有值
  size: existingData.size || updatedData.size,
  colorType: existingData.colorType || updatedData.colorType,
  
  // 動態屬性更新：優先使用新值
  battery: updatedData.battery !== undefined ? updatedData.battery : existingData.battery,
  rssi: updatedData.rssi !== undefined ? updatedData.rssi : existingData.rssi,
  imageCode: updatedData.imageCode !== undefined ? updatedData.imageCode : existingData.imageCode,
}
```

## 測試場景

### 場景1：初始載入
```
1. 打開設備管理頁面
2. API獲取完整設備數據
3. 驗證顯示：AA:BB:CC:DD:EE:FF - 2.9" | BW | 電量:85% | 信號:-45dBm
```

### 場景2：WebSocket更新（修復前會失敗）
```
1. 等待WebSocket自動更新（通常5-30秒）
2. 服務器發送：{ battery: 90, rssi: -50 }
3. 驗證顯示：AA:BB:CC:DD:EE:FF - 2.9" | BW | 電量:90% | 信號:-50dBm
   ✅ 尺寸和顏色保持不變
   ✅ 電量和信號正確更新
```

### 場景3：模板篩選測試
```
1. 點擊設備進入模板綁定頁面
2. 驗證設備信息顯示完整
3. 驗證模板篩選基於正確的設備規格
4. 確認只顯示2.9" BW兼容的模板
```

### 場景4：連續WebSocket更新
```
1. 等待多次WebSocket更新
2. 每次更新後驗證設備信息
3. 確認固定屬性始終保持正確
4. 確認動態屬性正確更新
```

## 預期結果

### ✅ 修復後的正確行為
- 設備尺寸信息在WebSocket更新後保持不變
- 設備顏色類型在WebSocket更新後保持不變  
- 電量和信號強度正確更新
- 模板篩選基於正確的設備規格
- 不需要手動刷新來恢復正確信息

### ❌ 修復前的錯誤行為
- WebSocket更新後尺寸變成undefined或空白
- WebSocket更新後顏色類型變成undefined或空白
- 需要手動下拉刷新才能恢復正確信息
- 模板篩選可能基於錯誤的設備規格

## 技術細節

### 數據合併策略
```typescript
// 固定屬性保護策略
size: existingData.size || updatedData.size
// 邏輯：如果現有數據有size，使用現有的；否則使用新數據的

// 動態屬性更新策略  
battery: updatedData.battery !== undefined ? updatedData.battery : existingData.battery
// 邏輯：如果新數據有battery，使用新的；否則保持現有的
```

### 屬性分類
```typescript
// 固定屬性（一旦設定不應改變）
const FIXED_PROPERTIES = ['size', 'colorType'];

// 動態屬性（會隨時間變化）
const DYNAMIC_PROPERTIES = ['battery', 'rssi', 'imageCode'];
```

## 調試信息

如果需要調試，可以在控制台查看：
```javascript
// 查看WebSocket更新事件
console.log('收到設備狀態更新事件:', event);

// 查看數據合併過程
console.log('現有設備數據:', existingData);
console.log('WebSocket更新數據:', updatedData);
console.log('合併後數據:', mergedData);
```

## 相關文件

### 修改的文件
- `epd-manager-app/src/stores/deviceStore.ts` (第261-300行)

### 測試相關文件
- `epd-manager-app/src/screens/DeviceManagementScreen.tsx` - 設備信息顯示
- `epd-manager-app/src/screens/DeviceOperationScreen.tsx` - 模板篩選邏輯

這個修復確保了設備的固定屬性（尺寸、顏色類型）在WebSocket實時更新過程中得到保護，同時允許動態屬性（電量、信號強度）正確更新。
