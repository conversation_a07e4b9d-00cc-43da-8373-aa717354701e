import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { FilterTabs, FilterOption } from '../FilterTabs';

describe('FilterTabs', () => {
  const mockOptions: FilterOption[] = [
    { key: 'all', label: '全部', count: 10 },
    { key: 'online', label: '在線', count: 7 },
    { key: 'offline', label: '離線', count: 3 },
  ];

  const mockOnSelect = jest.fn();

  beforeEach(() => {
    mockOnSelect.mockClear();
  });

  it('renders all filter options', () => {
    const { getByText } = render(
      <FilterTabs
        options={mockOptions}
        selectedKey="all"
        onSelect={mockOnSelect}
      />
    );

    expect(getByText('全部 (10)')).toBeTruthy();
    expect(getByText('在線 (7)')).toBeTruthy();
    expect(getByText('離線 (3)')).toBeTruthy();
  });

  it('calls onSelect when a tab is pressed', () => {
    const { getByText } = render(
      <FilterTabs
        options={mockOptions}
        selectedKey="all"
        onSelect={mockOnSelect}
      />
    );

    fireEvent.press(getByText('在線 (7)'));
    expect(mockOnSelect).toHaveBeenCalledWith('online');
  });

  it('highlights the selected filter', () => {
    const { getByText } = render(
      <FilterTabs
        options={mockOptions}
        selectedKey="online"
        onSelect={mockOnSelect}
      />
    );

    // The selected tab should have different styling
    // This is a basic test - in a real scenario you'd test the actual styles
    expect(getByText('在線 (7)')).toBeTruthy();
  });
});
