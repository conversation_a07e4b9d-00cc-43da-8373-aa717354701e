# EPD Manager App - 模板匹配測試指南

## 測試目標

驗證修復後的模板篩選功能能正確處理：
1. 尺寸格式轉換（顯示尺寸 ↔ 解析度）
2. 旋轉匹配（寬高 ↔ 高寬）
3. 顏色類型轉換（簡短格式 ↔ 完整格式）

## 測試準備

### 1. 測試數據設置
確保系統中有以下測試數據：

**設備數據**:
```
設備A: { size: "2.9\"", colorType: "BWR" }
設備B: { size: "128x296", colorType: "BWR" }
設備C: { size: "296x128", colorType: "Black & White & Red" }
設備D: { size: "4.2\"", colorType: "BWRY" }
```

**模板數據**:
```
模板1: { screenSize: "2.9\"", color: "BWR" }
模板2: { screenSize: "128x296", color: "Black & White & Red" }
模板3: { screenSize: "296x128", color: "BWR" }
模板4: { screenSize: "4.2\"", color: "BWRY" }
模板5: { screenSize: "240x416", color: "Black & White & Red & Yellow" }
```

## 測試場景

### 場景1：顯示尺寸 vs 解析度匹配
**操作**: 選擇設備A (2.9\", BWR)
**預期結果**: 
- ✅ 模板1 (2.9\", BWR) - 直接匹配
- ✅ 模板2 (128x296, Black & White & Red) - 尺寸轉換 + 顏色轉換
- ✅ 模板3 (296x128, BWR) - 尺寸轉換 + 旋轉匹配
- ❌ 模板4 (4.2\", BWRY) - 尺寸不匹配
- ❌ 模板5 (240x416, BWRY) - 尺寸和顏色都不匹配

### 場景2：解析度直接匹配
**操作**: 選擇設備B (128x296, BWR)
**預期結果**:
- ✅ 模板1 (2.9\", BWR) - 解析度轉換為顯示尺寸
- ✅ 模板2 (128x296, Black & White & Red) - 直接匹配 + 顏色轉換
- ✅ 模板3 (296x128, BWR) - 旋轉匹配
- ❌ 其他模板

### 場景3：旋轉匹配
**操作**: 選擇設備C (296x128, Black & White & Red)
**預期結果**:
- ✅ 模板1 (2.9\", BWR) - 旋轉匹配 + 顏色轉換
- ✅ 模板2 (128x296, Black & White & Red) - 旋轉匹配
- ✅ 模板3 (296x128, BWR) - 直接匹配 + 顏色轉換
- ❌ 其他模板

### 場景4：四色設備匹配
**操作**: 選擇設備D (4.2\", BWRY)
**預期結果**:
- ❌ 模板1-3 - 尺寸不匹配
- ✅ 模板4 (4.2\", BWRY) - 直接匹配
- ✅ 模板5 (240x416, Black & White & Red & Yellow) - 尺寸轉換 + 顏色轉換

## 測試步驟

### 1. 基本功能測試
1. 打開設備管理頁面
2. 點擊任一設備進入操作頁面
3. 查看"選擇模板"區域
4. 驗證顯示的兼容模板數量和內容

### 2. 調試信息檢查
1. 打開瀏覽器開發者工具
2. 查看控制台輸出
3. 驗證以下信息：
   ```
   設備篩選信息: {
     deviceSize: "2.9\"",
     deviceSizeStandard: "128x296",
     deviceSizeDisplay: "2.9\" (128x296)"
   }
   
   匹配檢查: {
     templateName: "商品標籤",
     templateSize: "128x296",
     templateSizeStandard: "128x296",
     sizeMatch: true,
     colorMatch: true
   }
   ```

### 3. 無匹配模板測試
1. 創建一個特殊尺寸的設備（如 "10.3\", BW"）
2. 確保沒有對應的模板
3. 驗證顯示調試信息：
   ```
   沒有找到與設備兼容的模板
   設備規格: 10.3\" (1024x758) | BW
   設備尺寸解析: 1024x758
   總模板數: 5
   可用模板: ...
   ```

### 4. 邊界情況測試
測試以下特殊情況：
- 設備尺寸為空或無效
- 模板尺寸格式異常
- 顏色類型為空或未知格式

## 驗證要點

### ✅ 成功標準
1. **尺寸匹配正確**: 
   - 顯示尺寸能匹配對應的解析度
   - 旋轉尺寸能正確匹配
   
2. **顏色匹配正確**:
   - 簡短格式能匹配完整格式
   - 反向匹配也能正常工作
   
3. **用戶體驗良好**:
   - 模板選單不再為空
   - 顯示正確的兼容模板數量
   - 調試信息清晰易懂

### ❌ 失敗標準
1. 模板選單仍然為空（當應該有匹配時）
2. 顯示了不兼容的模板
3. 控制台出現錯誤信息
4. 尺寸或顏色解析失敗

## 常見問題排查

### 問題1：模板選單仍然為空
**檢查**:
1. 設備尺寸格式是否正確
2. 模板尺寸格式是否正確
3. 屏幕配置是否包含該尺寸

**解決**:
1. 檢查 `screenConfigs` 中是否有對應配置
2. 驗證 `parseSizeString` 函數是否正確解析

### 問題2：匹配了不應該匹配的模板
**檢查**:
1. 尺寸匹配邏輯是否過於寬鬆
2. 顏色匹配邏輯是否有誤

**解決**:
1. 檢查 `isSizeMatch` 函數邏輯
2. 驗證顏色轉換函數

### 問題3：旋轉匹配不工作
**檢查**:
1. 寬高互換邏輯是否正確
2. 解析結果是否正確

**解決**:
1. 驗證 `parseSizeString` 返回的寬高值
2. 檢查旋轉匹配條件

## 測試報告模板

```
測試日期: ____
測試人員: ____

場景1 - 顯示尺寸vs解析度: [ ] 通過 [ ] 失敗
場景2 - 解析度直接匹配: [ ] 通過 [ ] 失敗  
場景3 - 旋轉匹配: [ ] 通過 [ ] 失敗
場景4 - 四色設備匹配: [ ] 通過 [ ] 失敗

調試信息檢查: [ ] 通過 [ ] 失敗
無匹配模板測試: [ ] 通過 [ ] 失敗
邊界情況測試: [ ] 通過 [ ] 失敗

總體評價: [ ] 通過 [ ] 需要修復

問題記錄:
1. ________________
2. ________________
3. ________________
```

通過這些測試，可以確保模板匹配功能的正確性和穩定性。
