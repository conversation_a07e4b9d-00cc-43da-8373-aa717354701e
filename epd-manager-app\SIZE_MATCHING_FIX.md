# EPD Manager App - 尺寸匹配修復

## 問題描述

尺寸也要轉換，一個是顯示尺寸一個是顯示解析度，應該統一換為解析度，只要寬高一樣就對了，要注意寬高與高寬都是可以允許的。

## 問題分析

### 根本原因
設備和模板使用不同的尺寸格式：
- **設備尺寸**: 可能是顯示尺寸（如 "2.9\""）或解析度（如 "128x296"）
- **模板尺寸**: 可能是顯示尺寸（如 "2.9\""）或解析度（如 "296x128"）

原有的匹配邏輯只做字符串直接比較，無法處理：
1. 不同格式之間的轉換
2. 寬高和高寬的旋轉情況

### 匹配失敗的場景
```
設備: size = "2.9\""
模板: screenSize = "128x296"
原邏輯: "2.9\"" === "128x296" → false ❌

設備: size = "128x296"
模板: screenSize = "296x128" (旋轉)
原邏輯: "128x296" === "296x128" → false ❌
```

## 修復方案

### 1. 創建屏幕配置映射
在 `src/utils/screenUtils.ts` 中定義標準屏幕配置：

```typescript
export const screenConfigs: ScreenConfig[] = [
  {
    id: "2_9inch", 
    name: "2.9\"",
    displayName: "2.9\" (128x296)",
    width: 128,
    height: 296,
    supportedColors: ["BWR", "Black & White & Red"]
  },
  // ... 其他配置
];
```

### 2. 尺寸解析函數
```typescript
export const parseSizeString = (sizeStr: string): { width: number; height: number } | null => {
  // 支持多種格式：
  // 1. "128x296" -> {width: 128, height: 296}
  // 2. "2.9\"" -> 查找配置 -> {width: 128, height: 296}
}
```

### 3. 智能尺寸匹配
```typescript
export const isSizeMatch = (size1: string, size2: string): boolean => {
  const dimensions1 = parseSizeString(size1);
  const dimensions2 = parseSizeString(size2);

  // 檢查直接匹配
  if (dimensions1.width === dimensions2.width && dimensions1.height === dimensions2.height) {
    return true;
  }

  // 檢查旋轉匹配（寬高互換）
  if (dimensions1.width === dimensions2.height && dimensions1.height === dimensions2.width) {
    return true;
  }

  return false;
}
```

## 支持的尺寸格式

### 輸入格式
| 格式 | 示例 | 解析結果 |
|------|------|----------|
| 顯示尺寸 | "2.9\"" | {width: 128, height: 296} |
| 解析度 | "128x296" | {width: 128, height: 296} |
| 旋轉解析度 | "296x128" | {width: 296, height: 128} |

### 匹配邏輯
| 設備尺寸 | 模板尺寸 | 匹配結果 | 說明 |
|----------|----------|----------|------|
| "2.9\"" | "128x296" | ✅ 匹配 | 顯示尺寸 → 解析度 |
| "128x296" | "2.9\"" | ✅ 匹配 | 解析度 → 顯示尺寸 |
| "128x296" | "296x128" | ✅ 匹配 | 旋轉匹配 |
| "2.9\"" | "2.9\"" | ✅ 匹配 | 直接匹配 |
| "2.9\"" | "4.2\"" | ❌ 不匹配 | 不同尺寸 |

## 測試場景

### 場景1：顯示尺寸 vs 解析度
```
設備: { size: "2.9\"", colorType: "BWR" }
模板: { screenSize: "128x296", color: "BWR" }
結果: ✅ 匹配成功
解析: "2.9\"" → {128, 296}, "128x296" → {128, 296}
```

### 場景2：旋轉匹配
```
設備: { size: "128x296", colorType: "BWR" }
模板: { screenSize: "296x128", color: "BWR" }
結果: ✅ 匹配成功
解析: 寬高互換匹配
```

### 場景3：複雜格式組合
```
設備: { size: "2.9\"", colorType: "BWR" }
模板: { screenSize: "296x128", color: "Black & White & Red" }
結果: ✅ 匹配成功
解析: 尺寸旋轉匹配 + 顏色格式轉換匹配
```

### 場景4：不匹配情況
```
設備: { size: "2.9\"", colorType: "BWR" }
模板: { screenSize: "240x416", color: "BWR" }
結果: ❌ 不匹配
解析: 尺寸不同（2.9\" ≠ 3.7\"）
```

## 調試功能增強

### 1. 詳細的控制台日誌
```
設備篩選信息: {
  deviceSize: "2.9\"",
  deviceColorType: "BWR",
  deviceSizeStandard: "128x296",
  deviceSizeDisplay: "2.9\" (128x296)",
  totalTemplates: 5
}

匹配檢查: {
  templateName: "商品標籤",
  templateSize: "128x296",
  templateSizeStandard: "128x296",
  templateColor: "BWR",
  deviceSize: "2.9\"",
  deviceSizeStandard: "128x296",
  sizeMatch: true,
  colorMatch: true
}
```

### 2. 用戶界面調試信息
```
沒有找到與設備兼容的模板
設備規格: 2.9\" (128x296) | BWR
設備尺寸解析: 128x296
總模板數: 5
可用模板: 商品標籤(2.9\" (128x296)|BWR), 價格標籤(4.2\" (240x416)|BWR), ...
```

## 修復效果

### 修復前
- 只能匹配完全相同的字符串格式
- 無法處理顯示尺寸和解析度之間的轉換
- 無法處理旋轉情況（寬高互換）
- 模板選單經常為空

### 修復後
- 支持多種尺寸格式的智能匹配
- 自動處理顯示尺寸和解析度的轉換
- 支持寬高和高寬的旋轉匹配
- 正確顯示所有兼容的模板

## 工具函數

### 核心函數
```typescript
// 解析尺寸字符串
parseSizeString(sizeStr: string): { width: number; height: number } | null

// 檢查尺寸匹配
isSizeMatch(size1: string, size2: string): boolean

// 獲取標準化尺寸字符串
getStandardSizeString(sizeStr: string): string | null

// 獲取尺寸顯示名稱
getSizeDisplayName(sizeStr: string): string
```

### 輔助函數
```typescript
// 獲取屏幕配置
getScreenConfigBySize(sizeStr: string): ScreenConfig | null

// 檢查尺寸顏色兼容性
isSizeColorCompatible(sizeStr: string, colorType: string): boolean

// 獲取支持的顏色類型
getSupportedColors(sizeStr: string): string[]
```

## 相關文件

### 新增文件
- `epd-manager-app/src/utils/screenUtils.ts` - 屏幕尺寸工具函數

### 修改文件
- `epd-manager-app/src/screens/DeviceOperationScreen.tsx` - 使用新的尺寸匹配邏輯

## 後續優化建議

1. **統一尺寸格式**: 考慮在系統中統一使用解析度格式
2. **配置化屏幕**: 將屏幕配置移到配置文件中
3. **自動檢測**: 根據設備報告自動檢測支持的尺寸和顏色
4. **模板驗證**: 在模板創建時驗證尺寸格式的正確性

這個修復確保了模板篩選功能能正確處理各種尺寸格式，解決了因尺寸格式不統一導致的匹配失敗問題。
