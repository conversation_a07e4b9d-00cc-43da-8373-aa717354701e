# EPD Manager App - 設備模板綁定功能

## 功能概述

為 EPD Manager App 添加了完整的設備模板綁定功能，允許用戶為設備選擇模板並配置數據綁定，實現個性化的顯示內容。

## 新增功能

### 1. 設備操作頁面 (DeviceOperationScreen)

- **位置**: `src/screens/DeviceOperationScreen.tsx`
- **功能**: 專用的設備操作界面，提供模板選擇和數據綁定配置
- **特點**:
  - 全屏模態展示，提供良好的用戶體驗
  - 顯示設備詳細信息（MAC地址、尺寸、電量、狀態等）
  - 水平滾動的模板選擇界面
  - 智能數據綁定配置
  - 立即發送到設備的選項

### 2. 模板選擇功能

- **模板卡片顯示**:
  - 模板預覽圖（支持base64格式）
  - 模板名稱和規格信息
  - 系統/門店模板標籤
  - 選中狀態視覺反饋

- **模板篩選**:
  - 自動載入當前門店可用的模板
  - 支持系統模板和門店專屬模板

### 3. 數據綁定配置

- **智能字段識別**:
  - 自動分析模板中的數據綁定字段
  - 支持新版和舊版數據綁定格式
  - 按數據索引組織字段

- **數據源選擇**:
  - 水平滾動的數據選項
  - 從門店數據中選擇綁定源
  - 視覺化的選中狀態

### 4. 設備管理頁面增強

- **新增操作按鈕**:
  - 在設備卡片中添加"模板綁定"按鈕
  - 點擊設備卡片直接進入操作頁面
  - 保留原有的"發送"功能

- **操作流程優化**:
  - 點擊設備 → 開啟操作頁面 → 選擇模板 → 配置數據 → 保存並發送

## 技術實現

### 1. 類型定義更新

```typescript
// 數據綁定信息接口
export interface DataBinding {
  dataIndex: number;
  fieldId: string;
  displayName?: string;
  displayOptions?: {
    showPrefix?: boolean;
  };
  selectedStoreId?: string;
}

// 設備綁定數據類型
export interface DeviceBindingData {
  templateId: string;
  dataBindings: Record<string, string>;
  sendToGateway?: boolean;
}
```

### 2. API 服務擴展

```typescript
// 新增API方法
async updateDeviceDataBindings(deviceId, bindingData, storeId)
async getStoreData(storeId)
```

### 3. 數據字段提取邏輯

- 支持新版 `dataBinding` 格式
- 兼容舊版 `dataFieldId` 格式
- 按數據索引創建唯一字段標識符
- 自動生成用戶友好的字段名稱

## 使用方式

### 1. 基本操作流程

1. 在設備管理頁面選擇要配置的設備
2. 點擊設備卡片或"模板綁定"按鈕
3. 在操作頁面中選擇合適的模板
4. 為需要數據的字段選擇數據源
5. 選擇是否立即發送到設備
6. 點擊"保存"完成配置

### 2. 模板選擇

- 水平滾動瀏覽可用模板
- 點擊模板卡片進行選擇
- 選中的模板會有視覺高亮
- 自動載入模板的數據字段要求

### 3. 數據綁定

- 系統自動識別模板中的數據字段
- 為每個字段提供數據源選擇
- 支持多個數據索引的獨立配置
- 實時驗證綁定完整性

## UI 設計特點

### 1. 清晰的視覺層次

- 使用卡片式設計組織信息
- 明確的區塊分割（設備信息、模板選擇、數據綁定）
- 一致的顏色和字體規範

### 2. 良好的交互體驗

- 觸摸友好的按鈕尺寸
- 清晰的選中狀態反饋
- 防誤觸的確認機制
- 載入狀態的視覺提示

### 3. 響應式布局

- 適配不同屏幕尺寸
- 水平滾動處理長列表
- 合理的間距和對齊

## 錯誤處理

- 網絡請求失敗的友好提示
- 數據驗證錯誤的具體說明
- 操作失敗的重試機制
- 載入狀態的視覺反饋

## 兼容性

- 支持新版數據綁定格式 (`dataBinding`)
- 兼容舊版數據綁定格式 (`dataFieldId`)
- 向下兼容現有設備配置
- 平滑的數據遷移

## 後續優化建議

1. **預覽功能**: 添加實時預覽圖生成
2. **批量操作**: 支持多設備批量模板綁定
3. **模板推薦**: 基於設備規格智能推薦模板
4. **歷史記錄**: 保存設備的綁定歷史
5. **離線支持**: 支持離線模式下的配置緩存
