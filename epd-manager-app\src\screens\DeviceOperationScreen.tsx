// EPD Manager App - 設備操作頁面

import React, { useEffect, useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  ActivityIndicator,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Device, Template, DataField, DeviceBindingData, shortCodeToDisplayColorType } from '../types';
import { useTemplates } from '../stores/templateStore';
import { useStores } from '../stores/storeStore';
import { COLORS, SIZES } from '../utils/constants';
import { apiService } from '../services/ApiService';

interface DeviceOperationScreenProps {
  visible: boolean;
  device: Device | null;
  onClose: () => void;
  onSuccess: () => void;
}

export const DeviceOperationScreen: React.FC<DeviceOperationScreenProps> = ({
  visible,
  device,
  onClose,
  onSuccess,
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [storeData, setStoreData] = useState<any[]>([]);
  const [dataBindings, setDataBindings] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [sendToGateway, setSendToGateway] = useState(false);
  
  const { selectedStore } = useStores();
  const { templates, fetchTemplates } = useTemplates();

  // 篩選出設備支援的模板
  const compatibleTemplates = useMemo(() => {
    if (!device || !device.data) return templates;

    const deviceSize = device.data.size;
    const deviceColorType = device.data.colorType;

    console.log('設備篩選信息:', {
      deviceSize,
      deviceColorType,
      totalTemplates: templates.length
    });

    return templates.filter(template => {
      // 檢查尺寸匹配
      const sizeMatch = template.screenSize === deviceSize;

      // 檢查顏色類型匹配
      let colorMatch = false;
      if (deviceColorType) {
        // 將設備的顏色類型轉換為完整格式進行比較
        const deviceFullColorType = shortCodeToDisplayColorType(deviceColorType);

        // 多種匹配方式：
        // 1. 直接匹配設備的原始顏色類型
        // 2. 匹配轉換後的完整格式
        // 3. 反向匹配（模板可能使用簡短格式）
        colorMatch = template.color === deviceColorType ||
                    template.color === deviceFullColorType ||
                    (deviceColorType === 'BW' && template.color === 'Gray16') ||
                    (deviceColorType === 'BWR' && template.color === 'Black & White & Red') ||
                    (deviceColorType === 'BWRY' && template.color === 'Black & White & Red & Yellow') ||
                    (deviceFullColorType === 'Gray16' && template.color === 'BW') ||
                    (deviceFullColorType === 'Black & White & Red' && template.color === 'BWR') ||
                    (deviceFullColorType === 'Black & White & Red & Yellow' && template.color === 'BWRY');

        console.log('顏色匹配檢查:', {
          templateName: template.name,
          templateColor: template.color,
          deviceColorType,
          deviceFullColorType,
          colorMatch
        });
      } else {
        // 如果設備沒有顏色類型信息，默認匹配BW相關格式
        colorMatch = template.color === 'Gray16' ||
                    template.color === 'BW' ||
                    template.color === '黑白';
      }

      const isMatch = sizeMatch && colorMatch;
      console.log('模板匹配結果:', {
        templateName: template.name,
        templateSize: template.screenSize,
        templateColor: template.color,
        sizeMatch,
        colorMatch,
        isMatch
      });

      return isMatch;
    });
  }, [templates, device]);

  useEffect(() => {
    if (visible && selectedStore) {
      loadTemplates();
      loadStoreData();
    }
  }, [visible, selectedStore]);

  useEffect(() => {
    // 當設備變化時，初始化綁定數據
    if (device) {
      initializeBindingData();
    }
  }, [device]);

  const loadTemplates = async () => {
    if (!selectedStore) return;
    await fetchTemplates(selectedStore.id);
  };

  const loadStoreData = async () => {
    if (!selectedStore) return;
    
    try {
      const response = await apiService.getStoreData(selectedStore.id);
      if (response.success && response.data) {
        setStoreData(response.data);
      }
    } catch (error) {
      console.error('載入門店數據失敗:', error);
    }
  };

  const initializeBindingData = () => {
    if (!device) return;

    // 初始化模板選擇
    if (device.templateId) {
      const template = compatibleTemplates.find(t => t.id === device.templateId);
      if (template) {
        setSelectedTemplate(template);
        loadTemplateDataFields(template);
      }
    }

    // 初始化數據綁定
    if (device.dataBindings) {
      try {
        const bindings = typeof device.dataBindings === 'string' 
          ? JSON.parse(device.dataBindings) 
          : device.dataBindings;
        setDataBindings(bindings);
      } catch (error) {
        console.error('解析數據綁定失敗:', error);
        setDataBindings({});
      }
    } else {
      setDataBindings({});
    }
  };

  const loadTemplateDataFields = async (template: Template) => {
    try {
      // 從模板元素中提取數據字段
      const fields: DataField[] = [];
      const fieldMap = new Map<string, DataField>();

      template.elements.forEach(element => {
        // 檢查新版數據綁定格式
        if (element.dataBinding?.fieldId) {
          const dataIndex = element.dataBinding.dataIndex || 0;
          const fieldId = element.dataBinding.fieldId;
          const uniqueFieldId = `${fieldId}_idx${dataIndex}`;

          if (!fieldMap.has(uniqueFieldId)) {
            fieldMap.set(uniqueFieldId, {
              id: uniqueFieldId,
              name: element.dataBinding.displayName || `數據${dataIndex + 1}`,
              type: element.type,
              description: `${element.type} 元素的數據字段 (索引${dataIndex})`
            });
          }
        }
        // 檢查舊版數據綁定格式
        else if (element.dataFieldId) {
          const match = element.dataFieldId.match(/(\d+)/);
          if (match) {
            const dataIndex = parseInt(match[1]) - 1;
            const fieldId = element.dataFieldId;
            const uniqueFieldId = `${fieldId}_idx${dataIndex}`;

            if (!fieldMap.has(uniqueFieldId)) {
              fieldMap.set(uniqueFieldId, {
                id: uniqueFieldId,
                name: `數據${dataIndex + 1}`,
                type: element.type,
                description: `${element.type} 元素的數據字段 (索引${dataIndex})`
              });
            }
          }
        }
      });

      const extractedFields = Array.from(fieldMap.values());
      setDataFields(extractedFields);
      console.log('提取的模板數據字段:', extractedFields);
    } catch (error) {
      console.error('載入模板數據字段失敗:', error);
      setDataFields([]);
    }
  };

  const handleTemplateSelect = async (template: Template) => {
    setSelectedTemplate(template);
    setDataBindings({}); // 清空之前的綁定
    await loadTemplateDataFields(template);
  };

  const handleDataBinding = (fieldId: string, dataId: string) => {
    setDataBindings(prev => ({
      ...prev,
      [fieldId]: dataId
    }));
  };

  const handleSubmit = async () => {
    if (!device || !selectedTemplate) {
      Alert.alert('錯誤', '請選擇模板');
      return;
    }

    // 檢查必要的數據綁定
    const missingBindings = dataFields.filter(field => !dataBindings[field.id]);
    if (missingBindings.length > 0 && dataFields.length > 0) {
      Alert.alert('錯誤', `請為以下字段選擇數據：${missingBindings.map(f => f.name).join(', ')}`);
      return;
    }

    setLoading(true);
    try {
      const bindingData: DeviceBindingData = {
        templateId: selectedTemplate.id,
        dataBindings,
        sendToGateway
      };

      const response = await apiService.updateDeviceDataBindings(
        device._id || device.macAddress,
        bindingData,
        selectedStore?.id
      );

      if (response.success) {
        Alert.alert('成功', '模板綁定已更新', [
          {
            text: '確定',
            onPress: () => {
              onSuccess();
              onClose();
            }
          }
        ]);
      } else {
        throw new Error(response.error || '更新失敗');
      }
    } catch (error: any) {
      console.error('更新設備綁定失敗:', error);
      Alert.alert('錯誤', error.message || '更新失敗，請稍後再試');
    } finally {
      setLoading(false);
    }
  };

  const renderTemplateCard = (template: Template) => (
    <TouchableOpacity
      key={template.id}
      style={[
        styles.templateCard,
        selectedTemplate?.id === template.id && styles.selectedTemplateCard
      ]}
      onPress={() => handleTemplateSelect(template)}
    >
      <View style={styles.templatePreview}>
        {template.previewImage ? (
          <Image
            source={{ uri: template.previewImage }}
            style={styles.previewImage}
            resizeMode="contain"
          />
        ) : (
          <View style={styles.noPreviewContainer}>
            <Text style={styles.noPreviewText}>無預覽</Text>
          </View>
        )}
      </View>
      <View style={styles.templateInfo}>
        <Text style={styles.templateName}>{template.name}</Text>
        <Text style={styles.templateDetails}>
          {template.screenSize} | {template.color}
        </Text>
        <View style={styles.templateTags}>
          <Text style={[
            styles.templateTag,
            template.isSystemTemplate ? styles.systemTag : styles.storeTag
          ]}>
            {template.isSystemTemplate ? '系統' : '門店'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderDataBindingSection = () => {
    if (!selectedTemplate || dataFields.length === 0) {
      return null;
    }

    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>數據綁定</Text>
        {dataFields.map(field => (
          <View key={field.id} style={styles.bindingRow}>
            <Text style={styles.fieldName}>{field.name}</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.dataOptions}>
                {storeData.map(data => (
                  <TouchableOpacity
                    key={data.id}
                    style={[
                      styles.dataOption,
                      dataBindings[field.id] === data.id && styles.selectedDataOption
                    ]}
                    onPress={() => handleDataBinding(field.id, data.id)}
                  >
                    <Text style={[
                      styles.dataOptionText,
                      dataBindings[field.id] === data.id && styles.selectedDataOptionText
                    ]}>
                      {data.name || data.id}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>
        ))}
      </View>
    );
  };

  if (!device) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* 頭部 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>取消</Text>
          </TouchableOpacity>
          <Text style={styles.title}>設備操作</Text>
          <TouchableOpacity 
            onPress={handleSubmit} 
            style={[styles.saveButton, loading && styles.disabledButton]}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color={COLORS.WHITE} />
            ) : (
              <Text style={styles.saveButtonText}>保存</Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          {/* 設備信息 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>設備信息</Text>
            <View style={styles.deviceInfo}>
              <Text style={styles.deviceMac}>{device.macAddress}</Text>
              <Text style={styles.deviceDetails}>
                {device.data?.size || '未知尺寸'} | {device.data?.colorType || 'BW'} |
                電量: {device.data?.battery || 0}% |
                信號: {device.data?.rssi || 0}dBm |
                狀態: {device.status === 'online' ? '在線' : '離線'}
              </Text>
            </View>
          </View>

          {/* 模板選擇 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              選擇模板 ({compatibleTemplates.length} 個兼容模板)
            </Text>
            {compatibleTemplates.length === 0 ? (
              <View style={styles.noTemplatesContainer}>
                <Text style={styles.noTemplatesText}>
                  沒有找到與設備兼容的模板
                </Text>
                <Text style={styles.debugText}>
                  設備規格: {device.data?.size} | {device.data?.colorType}
                </Text>
                <Text style={styles.debugText}>
                  總模板數: {templates.length}
                </Text>
                {templates.length > 0 && (
                  <Text style={styles.debugText}>
                    可用模板: {templates.map(t => `${t.name}(${t.screenSize}|${t.color})`).join(', ')}
                  </Text>
                )}
              </View>
            ) : (
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View style={styles.templatesContainer}>
                  {compatibleTemplates.map(renderTemplateCard)}
                </View>
              </ScrollView>
            )}
          </View>

          {/* 數據綁定 */}
          {renderDataBindingSection()}

          {/* 發送選項 */}
          <View style={styles.section}>
            <TouchableOpacity
              style={styles.checkboxRow}
              onPress={() => setSendToGateway(!sendToGateway)}
            >
              <View style={[styles.checkbox, sendToGateway && styles.checkedCheckbox]}>
                {sendToGateway && <Text style={styles.checkmark}>✓</Text>}
              </View>
              <Text style={styles.checkboxLabel}>立即發送到設備</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
    backgroundColor: COLORS.WHITE,
  },
  closeButton: {
    paddingVertical: SIZES.SPACING_XS,
    paddingHorizontal: SIZES.SPACING_SM,
  },
  closeButtonText: {
    color: COLORS.PRIMARY,
    fontSize: SIZES.FONT_MD,
    fontWeight: '500',
  },
  title: {
    fontSize: SIZES.FONT_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  saveButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SIZES.SPACING_XS,
    paddingHorizontal: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    minWidth: 60,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: COLORS.TEXT_SECONDARY,
  },
  saveButtonText: {
    color: COLORS.WHITE,
    fontSize: SIZES.FONT_MD,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: SIZES.SPACING_MD,
  },
  section: {
    marginBottom: SIZES.SPACING_LG,
  },
  sectionTitle: {
    fontSize: SIZES.FONT_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_SM,
  },
  deviceInfo: {
    backgroundColor: COLORS.WHITE,
    padding: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  deviceMac: {
    fontSize: SIZES.FONT_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_XS,
  },
  deviceDetails: {
    fontSize: SIZES.FONT_SM,
    color: COLORS.TEXT_SECONDARY,
  },
  templatesContainer: {
    flexDirection: 'row',
    paddingHorizontal: SIZES.SPACING_XS,
  },
  templateCard: {
    width: 160,
    backgroundColor: COLORS.WHITE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    marginRight: SIZES.SPACING_SM,
    overflow: 'hidden',
  },
  selectedTemplateCard: {
    borderColor: COLORS.PRIMARY,
    borderWidth: 2,
  },
  templatePreview: {
    height: 100,
    backgroundColor: COLORS.BACKGROUND,
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewImage: {
    width: '100%',
    height: '100%',
  },
  noPreviewContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  noPreviewText: {
    color: COLORS.TEXT_SECONDARY,
    fontSize: SIZES.FONT_SM,
  },
  templateInfo: {
    padding: SIZES.SPACING_SM,
  },
  templateName: {
    fontSize: SIZES.FONT_SM,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_XS,
  },
  templateDetails: {
    fontSize: SIZES.FONT_XS,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_XS,
  },
  templateTags: {
    flexDirection: 'row',
  },
  templateTag: {
    fontSize: SIZES.FONT_XS,
    paddingHorizontal: SIZES.SPACING_XS,
    paddingVertical: 2,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    overflow: 'hidden',
  },
  systemTag: {
    backgroundColor: COLORS.SUCCESS + '20',
    color: COLORS.SUCCESS,
  },
  storeTag: {
    backgroundColor: COLORS.PRIMARY + '20',
    color: COLORS.PRIMARY,
  },
  bindingRow: {
    marginBottom: SIZES.SPACING_MD,
  },
  fieldName: {
    fontSize: SIZES.FONT_SM,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_XS,
  },
  dataOptions: {
    flexDirection: 'row',
    paddingHorizontal: SIZES.SPACING_XS,
  },
  dataOption: {
    backgroundColor: COLORS.WHITE,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    marginRight: SIZES.SPACING_XS,
  },
  selectedDataOption: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  dataOptionText: {
    fontSize: SIZES.FONT_SM,
    color: COLORS.TEXT_PRIMARY,
  },
  selectedDataOptionText: {
    color: COLORS.WHITE,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    padding: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: COLORS.BORDER,
    borderRadius: 4,
    marginRight: SIZES.SPACING_SM,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedCheckbox: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  checkmark: {
    color: COLORS.WHITE,
    fontSize: 12,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: SIZES.FONT_MD,
    color: COLORS.TEXT_PRIMARY,
  },
  noTemplatesContainer: {
    backgroundColor: COLORS.WHITE,
    padding: SIZES.SPACING_LG,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    alignItems: 'center',
  },
  noTemplatesText: {
    fontSize: SIZES.FONT_SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SIZES.SPACING_XS,
  },
  debugText: {
    fontSize: SIZES.FONT_XS,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SIZES.SPACING_XS,
  },
});
