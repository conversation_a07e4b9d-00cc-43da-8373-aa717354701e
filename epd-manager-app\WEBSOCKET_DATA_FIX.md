# EPD Manager App - WebSocket 數據更新修復

## 問題描述

用戶反映：
> 下拉刷新的數據都對，但ws自動更新後變成尺寸錯的，顏色也是錯的，要再次下拉刷新才會對，被更新後又錯了

## 問題分析

### 根本原因
WebSocket 自動更新時，服務器端只廣播了部分設備數據，導致APP端接收到的設備信息不完整：

**修復前的廣播數據結構**:
```javascript
const deviceUpdates = processedDevices.map(device => ({
  _id: device._id.toString(),
  macAddress: device.macAddress,
  status: device.status,
  lastSeen: device.lastSeen.toISOString(),
  imageUpdateStatus: device.imageUpdateStatus,
  data: {
    battery: device.data?.battery,    // ✓ 包含
    rssi: device.data?.rssi,         // ✓ 包含
    imageCode: device.data?.imageCode // ✓ 包含
    // ❌ 缺少 size
    // ❌ 缺少 colorType
  },
  updatedFields: ['status', 'lastSeen', 'data']
}));
```

### 數據流程對比

#### API 獲取數據（正確）
```
API請求 → 數據庫 → 完整設備數據 → APP顯示
包含: size, colorType, battery, rssi, imageCode 等
```

#### WebSocket 更新數據（修復前 - 有問題）
```
設備狀態更新 → 服務器處理 → 廣播部分數據 → APP接收不完整數據
只包含: battery, rssi, imageCode
缺少: size, colorType
```

#### WebSocket 更新數據（修復後 - 正確）
```
設備狀態更新 → 服務器處理 → 廣播完整數據 → APP接收完整數據
包含: size, colorType, battery, rssi, imageCode 等
```

## 修復方案

### 服務器端修復
在 `server/services/websocketService.js` 的第1247-1261行，修改設備更新廣播邏輯：

**修復前**:
```javascript
data: {
  battery: device.data?.battery,
  rssi: device.data?.rssi,
  imageCode: device.data?.imageCode
}
```

**修復後**:
```javascript
data: {
  size: device.data?.size,          // ✅ 新增
  battery: device.data?.battery,
  rssi: device.data?.rssi,
  colorType: device.data?.colorType, // ✅ 新增
  imageCode: device.data?.imageCode
}
```

### APP端處理邏輯（已正確）
APP端的 `deviceStore.ts` 中的WebSocket處理邏輯是正確的，它會合併更新的設備數據：

```typescript
updatedDevices[index] = {
  ...updatedDevices[index],  // 保留現有數據
  ...updatedDevice,          // 合併新數據
  lastSeen: updatedDevice.lastSeen ? new Date(updatedDevice.lastSeen) : null,
  updatedAt: updatedDevice.updatedAt ? new Date(updatedDevice.updatedAt) : null,
};
```

## 修復效果

### 修復前的問題流程
1. 用戶下拉刷新 → 獲取完整設備數據 → 顯示正確的尺寸和顏色
2. WebSocket 自動更新 → 接收不完整數據 → 尺寸和顏色信息丟失
3. 用戶再次下拉刷新 → 重新獲取完整數據 → 顯示恢復正常
4. 循環重複...

### 修復後的正常流程
1. 用戶下拉刷新 → 獲取完整設備數據 → 顯示正確的尺寸和顏色
2. WebSocket 自動更新 → 接收完整數據 → 尺寸和顏色信息保持正確
3. 持續的WebSocket更新 → 所有信息都保持正確

## 測試驗證

### 測試場景
1. **初始載入測試**
   - 打開設備管理頁面
   - 驗證設備信息顯示完整（尺寸、顏色、電量、信號）

2. **WebSocket更新測試**
   - 等待WebSocket自動更新
   - 驗證設備信息在更新後仍然完整
   - 特別關注尺寸和顏色信息是否保持正確

3. **模板篩選測試**
   - 點擊設備進入模板綁定頁面
   - 驗證模板篩選是否基於正確的設備尺寸和顏色
   - 確認只顯示兼容的模板

### 預期結果
- ✅ 設備信息在WebSocket更新後保持完整
- ✅ 尺寸信息不會丟失
- ✅ 顏色類型信息不會丟失
- ✅ 模板篩選基於正確的設備規格
- ✅ 不需要手動刷新來恢復正確信息

## 相關文件

### 修改的文件
- `server/services/websocketService.js` (第1247-1261行)

### 相關文件（無需修改）
- `epd-manager-app/src/stores/deviceStore.ts` - WebSocket處理邏輯
- `epd-manager-app/src/screens/DeviceManagementScreen.tsx` - 設備信息顯示
- `epd-manager-app/src/screens/DeviceOperationScreen.tsx` - 模板篩選邏輯

## 技術細節

### WebSocket 數據結構
修復後的完整WebSocket設備更新事件：

```typescript
interface DeviceStatusEvent {
  type: 'device_status_update';
  storeId: string;
  devices: Array<{
    _id: string;
    macAddress: string;
    status: 'online' | 'offline';
    lastSeen: string;
    imageUpdateStatus?: '已更新' | '未更新';
    data: {
      size: string;        // ✅ 現在包含
      battery: number;
      rssi: number;
      colorType: string;   // ✅ 現在包含
      imageCode?: string;
    };
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'single' | 'batch';
}
```

### 數據合併邏輯
APP端使用展開運算符合併數據，確保新數據覆蓋舊數據，同時保留未更新的字段：

```typescript
updatedDevices[index] = {
  ...updatedDevices[index],  // 現有數據作為基礎
  ...updatedDevice,          // 新數據覆蓋對應字段
  // 特殊處理日期字段
  lastSeen: updatedDevice.lastSeen ? new Date(updatedDevice.lastSeen) : null,
  updatedAt: updatedDevice.updatedAt ? new Date(updatedDevice.updatedAt) : null,
};
```

這個修復確保了WebSocket實時更新的數據完整性，解決了用戶反映的尺寸和顏色信息丟失問題。
