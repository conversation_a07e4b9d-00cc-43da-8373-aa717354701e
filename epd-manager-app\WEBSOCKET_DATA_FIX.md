# EPD Manager App - WebSocket 數據更新修復

## 問題描述

用戶反映：
> 下拉刷新的數據都對，但ws自動更新後變成尺寸錯的，顏色也是錯的，要再次下拉刷新才會對，被更新後又錯了

## 問題分析

### 根本原因
WebSocket 自動更新時，服務器端只廣播了部分設備數據（battery, rssi, imageCode），而APP端的合併邏輯會讓缺失的字段（size, colorType）被undefined覆蓋，導致設備的固定屬性丟失。

### 設計原則問題
設備的某些屬性應該分為兩類：
- **固定屬性**: size（尺寸）、colorType（顏色類型）- 這些在設備初始化後不應該改變
- **動態屬性**: battery（電量）、rssi（信號強度）、imageCode（圖片代碼）- 這些會隨時間變化

**服務器端廣播數據結構**（正確，只包含會變化的數據）:
```javascript
const deviceUpdates = processedDevices.map(device => ({
  _id: device._id.toString(),
  macAddress: device.macAddress,
  status: device.status,
  lastSeen: device.lastSeen.toISOString(),
  imageUpdateStatus: device.imageUpdateStatus,
  data: {
    battery: device.data?.battery,    // ✓ 動態屬性
    rssi: device.data?.rssi,         // ✓ 動態屬性
    imageCode: device.data?.imageCode // ✓ 動態屬性
    // 不包含 size 和 colorType（固定屬性）
  },
  updatedFields: ['status', 'lastSeen', 'data']
}));
```

### 數據流程對比

#### API 獲取數據（正確）
```
API請求 → 數據庫 → 完整設備數據 → APP顯示
包含: size, colorType, battery, rssi, imageCode 等
```

#### WebSocket 更新數據（修復前 - 有問題）
```
設備狀態更新 → 服務器廣播動態數據 → APP直接合併覆蓋 → 固定屬性丟失
服務器發送: battery, rssi, imageCode
APP處理: {...existingDevice, ...updatedDevice}
結果: size和colorType被undefined覆蓋
```

#### WebSocket 更新數據（修復後 - 正確）
```
設備狀態更新 → 服務器廣播動態數據 → APP智能合併 → 固定屬性保護
服務器發送: battery, rssi, imageCode
APP處理: 保護固定屬性，只更新動態屬性
結果: size和colorType保持不變，動態屬性正確更新
```

## 修復方案

### APP端修復（正確的解決方案）
在 `epd-manager-app/src/stores/deviceStore.ts` 中修改WebSocket設備更新邏輯，保護固定屬性不被覆蓋：

**修復前（有問題的邏輯）**:
```typescript
updatedDevices[index] = {
  ...updatedDevices[index],  // 現有數據
  ...updatedDevice,          // 新數據直接覆蓋（問題所在）
  lastSeen: updatedDevice.lastSeen ? new Date(updatedDevice.lastSeen) : null,
  updatedAt: updatedDevice.updatedAt ? new Date(updatedDevice.updatedAt) : null,
};
```

**修復後（智能合併邏輯）**:
```typescript
const existingDevice = updatedDevices[index];
const existingData = existingDevice.data || {};
const updatedData = updatedDevice.data || {};

updatedDevices[index] = {
  ...existingDevice,
  ...updatedDevice,
  data: {
    // 保護固定屬性：size 和 colorType 一旦設定就不應該被覆蓋
    size: existingData.size || updatedData.size,
    colorType: existingData.colorType || updatedData.colorType,
    // 允許動態屬性更新
    battery: updatedData.battery !== undefined ? updatedData.battery : existingData.battery,
    rssi: updatedData.rssi !== undefined ? updatedData.rssi : existingData.rssi,
    imageCode: updatedData.imageCode !== undefined ? updatedData.imageCode : existingData.imageCode,
    // 保留其他數據字段
    ...existingData,
    ...Object.fromEntries(
      Object.entries(updatedData).filter(([key]) =>
        !['size', 'colorType', 'battery', 'rssi', 'imageCode'].includes(key)
      )
    )
  },
  lastSeen: updatedDevice.lastSeen ? new Date(updatedDevice.lastSeen) : existingDevice.lastSeen,
  updatedAt: updatedDevice.updatedAt ? new Date(updatedDevice.updatedAt) : existingDevice.updatedAt,
};
```

### 服務器端（保持不變）
服務器端的廣播邏輯是正確的，只發送會變化的動態屬性，不需要修改。

## 修復效果

### 修復前的問題流程
1. 用戶下拉刷新 → 獲取完整設備數據 → 顯示正確的尺寸和顏色
2. WebSocket 自動更新 → 接收不完整數據 → 尺寸和顏色信息丟失
3. 用戶再次下拉刷新 → 重新獲取完整數據 → 顯示恢復正常
4. 循環重複...

### 修復後的正常流程
1. 用戶下拉刷新 → 獲取完整設備數據 → 顯示正確的尺寸和顏色
2. WebSocket 自動更新 → 接收完整數據 → 尺寸和顏色信息保持正確
3. 持續的WebSocket更新 → 所有信息都保持正確

## 測試驗證

### 測試場景
1. **初始載入測試**
   - 打開設備管理頁面
   - 驗證設備信息顯示完整（尺寸、顏色、電量、信號）

2. **WebSocket更新測試**
   - 等待WebSocket自動更新
   - 驗證設備信息在更新後仍然完整
   - 特別關注尺寸和顏色信息是否保持正確

3. **模板篩選測試**
   - 點擊設備進入模板綁定頁面
   - 驗證模板篩選是否基於正確的設備尺寸和顏色
   - 確認只顯示兼容的模板

### 預期結果
- ✅ 設備信息在WebSocket更新後保持完整
- ✅ 尺寸信息不會丟失
- ✅ 顏色類型信息不會丟失
- ✅ 模板篩選基於正確的設備規格
- ✅ 不需要手動刷新來恢復正確信息

## 相關文件

### 修改的文件
- `server/services/websocketService.js` (第1247-1261行)

### 相關文件（無需修改）
- `epd-manager-app/src/stores/deviceStore.ts` - WebSocket處理邏輯
- `epd-manager-app/src/screens/DeviceManagementScreen.tsx` - 設備信息顯示
- `epd-manager-app/src/screens/DeviceOperationScreen.tsx` - 模板篩選邏輯

## 技術細節

### WebSocket 數據結構
修復後的完整WebSocket設備更新事件：

```typescript
interface DeviceStatusEvent {
  type: 'device_status_update';
  storeId: string;
  devices: Array<{
    _id: string;
    macAddress: string;
    status: 'online' | 'offline';
    lastSeen: string;
    imageUpdateStatus?: '已更新' | '未更新';
    data: {
      size: string;        // ✅ 現在包含
      battery: number;
      rssi: number;
      colorType: string;   // ✅ 現在包含
      imageCode?: string;
    };
    updatedFields: string[];
  }>;
  timestamp: string;
  updateType: 'single' | 'batch';
}
```

### 數據合併邏輯
APP端使用展開運算符合併數據，確保新數據覆蓋舊數據，同時保留未更新的字段：

```typescript
updatedDevices[index] = {
  ...updatedDevices[index],  // 現有數據作為基礎
  ...updatedDevice,          // 新數據覆蓋對應字段
  // 特殊處理日期字段
  lastSeen: updatedDevice.lastSeen ? new Date(updatedDevice.lastSeen) : null,
  updatedAt: updatedDevice.updatedAt ? new Date(updatedDevice.updatedAt) : null,
};
```

這個修復確保了WebSocket實時更新的數據完整性，解決了用戶反映的尺寸和顏色信息丟失問題。
